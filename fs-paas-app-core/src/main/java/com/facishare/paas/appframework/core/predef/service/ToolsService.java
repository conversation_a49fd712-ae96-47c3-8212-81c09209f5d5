package com.facishare.paas.appframework.core.predef.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.privilege.service.UserService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.layout.CreateOrUpdateLayouts;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.CreateOrUpdateDescribe;
import com.facishare.paas.appframework.core.predef.service.dto.tools.GetEnterpriseIds;
import com.facishare.paas.appframework.core.predef.service.dto.tools.GetHandler;
import com.facishare.paas.appframework.core.predef.service.dto.tools.UpsertHandlerDefinitionAndRuntimeConfig;
import com.facishare.paas.appframework.metadata.dto.tools.ValidateHandler;
import com.facishare.paas.appframework.metadata.dto.tools.*;
import com.facishare.paas.appframework.metadata.tools.InitToolService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@ServiceModule("tools")
@Service
@Slf4j
public class ToolsService {

    @Resource(name = "initToolService")
    private InitToolService initToolService;
    @Autowired
    private UserService userService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;

    @ServiceMethod("getEnterpriseIds")
    public GetEnterpriseIds.Result getEnterpriseIds(GetEnterpriseIds.Arg arg) {
        List<Integer> enterpriseList = initToolService.getEnterpriseList(arg.getEnv(), arg.getRunStatusList());
        return GetEnterpriseIds.Result.builder().enterpriseIds(enterpriseList).build();
    }

    @ServiceMethod("transferBrush")
    public Map<String, Object> transferBrush(TransferBrush.Arg arg) {
        initToolService.transferExecute(arg);
        return Maps.newHashMap();
    }


    @ServiceMethod("/transferBrushBiComponent")
    public Map<String, Object> transferBrushBiComponent(TransferBrushBiComponent.Arg arg) {
        initToolService.transferBrushBiComponent(arg);
        return Maps.newHashMap();
    }

    @ServiceMethod("progressControl")
    public BrushProgress.Result progressControl(BrushProgress.Arg arg) {
        return initToolService.progressControl(arg);
    }

    @ServiceMethod("createOrUpdateDescribe")
    public CreateOrUpdateDescribe.Result createOrUpdateDescribe(ServiceContext context, CreateOrUpdateDescribe.Arg arg) {
        BrushDescribe.Arg brushDescribeArg = BrushDescribe.Arg.builder()
                .describeApiName(arg.getDescribeApiName())
                .describeJson(arg.getDescribeJson())
                .describeAttribute(arg.getDescribeAttribute())
                .describeExtraAttribute(arg.getDescribeExtraAttribute())
                .fields(arg.getFields())
                .fieldExtraAttribute(arg.getFieldExtraAttribute())
                .build();
        initToolService.executeBrushDescribe(brushDescribeArg, arg.getTenantId());
        return CreateOrUpdateDescribe.Result.builder().build();
    }

    /**
     * 初始化工具使用，单独灰度fs企业
     *
     * @param arg
     * @param context
     * @return
     */
    @ServiceMethod("createOrUpdateLayouts")
    public CreateOrUpdateLayouts.Result createOrUpdateLayouts(ServiceContext context, CreateOrUpdateLayouts.Arg arg) {
        BrushLayouts.Arg brushLayoutsArg = BrushLayouts.Arg.builder()
                .detailLayout(arg.getDetailLayout())
                .mobileLayout(arg.getMobileLayout())
                .listLayout(arg.getListLayout())
                .addEditLayout(arg.getAddEditLayout())
                .describeApiName(arg.getDescribeApiName())
                .tenantId(arg.getTenantId())
                .build();
        initToolService.executeBrushLayout(brushLayoutsArg, arg.getTenantId());
        return CreateOrUpdateLayouts.Result.builder().build();
    }

    @ServiceMethod("findHandlerDescribes")
    public GetHandler.Result queryHandler(ServiceContext context, GetHandler.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getObjectApiName(), arg.getInterfaceCode(), arg.getTenantId())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<HandlerDescribe> handlerDescribes = initToolService.findHandlerDescribes(User.systemUser(arg.getTenantId()), arg.getObjectApiName(), arg.getInterfaceCode());
        return GetHandler.Result.builder()
                .handlerDescribes(handlerDescribes)
                .build();
    }

    /**
     * 验证handler参数
     *
     * @param arg
     * @return
     */
    @ServiceMethod("validateHandler")
    public ValidateHandler.Result validateHandler(ValidateHandler.Arg arg) {
        List<HandlerDescribe> handlerDescribes = arg.getHandlerDescribes();
        if (StringUtils.isAnyEmpty(arg.getObjectApiName(), arg.getInterfaceCode()) || CollectionUtils.empty(handlerDescribes)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return initToolService.validateHandler(User.systemUser(arg.getTenantId()), handlerDescribes, arg.getObjectApiName(), arg.getInterfaceCode());
    }


    @ServiceMethod("upsertHandlerDefinitionAndRuntimeConfig")
    public UpsertHandlerDefinitionAndRuntimeConfig.Result upsertHandlerDefinitionAndRuntimeConfig(UpsertHandlerDefinitionAndRuntimeConfig.Arg arg) {
        List<HandlerDescribe> handlerDescribes = arg.getHandlerDescribes();
        if (StringUtils.isAnyEmpty(arg.getDescribeApiName(), arg.getInterfaceCode()) || CollectionUtils.empty(handlerDescribes)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        String upsertResult = initToolService.upsertHandlerDefinitionAndRuntimeConfig(User.systemUser(arg.getTenantId()), handlerDescribes, arg.getDescribeApiName(), arg.getInterfaceCode());
        return UpsertHandlerDefinitionAndRuntimeConfig.Result.builder().result(upsertResult).build();
    }

    @ServiceMethod("transferBrushHandler")
    public BrushHandler.Result transferBrushHandler(BrushHandler.Arg arg) {
        String message = initToolService.transferExecute(arg);
        return BrushHandler.Result.builder().message(message).build();
    }


    /**
     * 根据手机号查人员信息
     *
     * @param context 服务上下文
     * @param arg     请求参数
     * @return 人员信息结果
     */
    @ServiceMethod("findPersonByMobile")
    public FindPersonByMobile.Result findPersonByMobile(ServiceContext context, FindPersonByMobile.Arg arg) {
        if (StringUtils.isBlank(arg.getEi()) || StringUtils.isEmpty(arg.getPhone())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        EmployeeDto employeeByMobile = employeeService.getEmployeeByMobile(arg.getEi(), arg.getPhone());
        String userId = null;
        String roleCode = null;
        String roleName = null;
        if (Objects.nonNull(employeeByMobile)) {
            userId = String.valueOf(employeeByMobile.getEmployeeId());

            if (arg.isIncludeMainRole()) {
                User user = User.builder()
                        .tenantId(arg.getEi())
                        .userId(String.valueOf(employeeByMobile.getEmployeeId()))
                        .build();
                Optional<String> mainRoleCode = userRoleInfoService.getDefaultRoleCode(user);
                if (mainRoleCode.isPresent()) {
                    roleCode = mainRoleCode.get();
                    if (arg.isIncludeMainRoleName() && StringUtils.isNotBlank(roleCode)) {
                        List<GetUserRoleInfo.RoleInfo> roleInfos = userRoleInfoService.queryRoleInfoByRoleCode(user, Lists.newArrayList(roleCode));
                        if (CollectionUtils.notEmpty(roleInfos) && Objects.nonNull(roleInfos.get(0))) {
                            roleName = roleInfos.get(0).getRoleName();
                        }
                    }
                }
            }
        }
        return FindPersonByMobile.Result.builder()
                .userId(userId)
                .mainRoleCode(roleCode)
                .mainRoleName(roleName)
                .build();
    }
}
