package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;
import java.lang.reflect.Field;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardUnionInsertImportVerifyAction的JUnit 5测试类
 * 测试标准联合新建导入校验Action的核心功能
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StandardUnionInsertImportVerifyActionTest {

    // 测试数据常量
    private static final String TENANT_ID = "test_tenant";
    private static final String USER_ID = "test_user";
    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String ACTION_CODE = "UnionInsertImportVerify";

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IFieldDescribe fieldDescribe;

    private StandardUnionInsertImportVerifyAction action;
    private ActionContext actionContext;
    private User user;
    private RequestContext requestContext;
    private BaseImportVerifyAction.Arg arg;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 初始化参数
        ObjectDataDocument row1 = new ObjectDataDocument();
        row1.put("name", "Union Insert Object 1");
        row1.put("description", "Union Insert Description 1");
        row1.put("email", "<EMAIL>");
        
        ObjectDataDocument row2 = new ObjectDataDocument();
        row2.put("name", "Union Insert Object 2");
        row2.put("description", "Union Insert Description 2");
        row2.put("email", "<EMAIL>");
        
        arg = new BaseImportAction.Arg();
        arg.setRows(Arrays.asList(row1, row2));
        arg.setImportType(BaseImportAction.IMPORT_TYPE_ADD); // INSERT type (0)
        arg.setMatchingType(BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE);

        // 初始化被测试对象
        action = new StandardUnionInsertImportVerifyAction();
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(action, "arg", arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardUnionInsertImportVerifyAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 基本功能测试")
    void testBasicFunctionality() {
        // Act & Assert: 验证对象初始化
        assertNotNull(action);
        assertNotNull(Whitebox.getInternalState(action, "serviceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "infraServiceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "actionContext"));
        assertNotNull(Whitebox.getInternalState(action, "objectDescribe"));
        assertNotNull(Whitebox.getInternalState(action, "arg"));

        // 验证基本属性
        ActionContext ctx = Whitebox.getInternalState(action, "actionContext");
        assertEquals(TENANT_ID, ctx.getTenantId());
        assertEquals(OBJECT_API_NAME, ctx.getObjectApiName());
        assertEquals(ACTION_CODE, ctx.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法，验证功能权限代码获取逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction getFuncPrivilegeCodes - 功能权限代码获取测试")
    void testGetFuncPrivilegeCodes() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> result = action.getFuncPrivilegeCodes();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(StandardAction.UnionInsertImportVerify.getFunPrivilegeCodes(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法，验证数据权限ID获取逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction getDataPrivilegeIds - 数据权限ID获取测试")
    void testGetDataPrivilegeIds() {
        // Act: 调用getDataPrivilegeIds方法
        List<String> result = action.getDataPrivilegeIds(arg);

        // Assert: 验证结果（联合新建导入校验不需要数据权限ID）
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试recordLog方法，验证日志记录逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction recordLog - 日志记录测试")
    void testRecordLog() {
        // Arrange: 配置Mock行为
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.getDisplayName()).thenReturn("测试对象");
        doNothing().when(serviceFacade).log(any(User.class), any(EventType.class), any(ActionType.class), any(String.class), any(String.class));

        // Act: 调用recordLog方法
        action.recordLog();

        // Assert: 验证Mock交互
        verify(serviceFacade).log(user, EventType.ADD, ActionType.Import, OBJECT_API_NAME, "测试对象");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getValidImportFields方法，验证有效导入字段获取逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction getValidImportFields - 有效导入字段获取测试")
    void testGetValidImportFields() {
        // Arrange: 配置Mock行为
        List<IFieldDescribe> mockFields = Arrays.asList(fieldDescribe);
        when(infraServiceFacade.getTemplateField(user, objectDescribe)).thenReturn(mockFields);
        when(fieldDescribe.getApiName()).thenReturn("name");
        when(fieldDescribe.getLabel()).thenReturn("名称");

        // Act: 调用getValidImportFields方法
        List<IFieldDescribe> result = action.getValidImportFields();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(fieldDescribe, result.get(0));

        // 验证Mock交互
        verify(infraServiceFacade).getTemplateField(user, objectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试customVerify方法，验证自定义校验逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction customVerify - 自定义校验测试")
    void testCustomVerify() {
        // Act: 调用customVerify方法
        String result = action.customVerify();

        // Assert: 验证结果（联合新建导入校验没有自定义校验）
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试继承关系，验证StandardUnionInsertImportVerifyAction继承BaseImportVerifyAction
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 继承关系测试")
    void testInheritanceRelationship() {
        // Act & Assert: 验证继承关系
        assertTrue(action instanceof BaseImportVerifyAction);
        
        // 验证类型转换
        BaseImportVerifyAction baseAction = (BaseImportVerifyAction) action;
        assertNotNull(baseAction);
        
        // 验证类名
        assertEquals("StandardUnionInsertImportVerifyAction", action.getClass().getSimpleName());
        assertEquals("StandardInsertImportVerifyAction", action.getClass().getSuperclass().getSimpleName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的各种属性，验证参数类的功能
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction Arg参数类测试")
    void testArgClass() {
        // Act & Assert: 验证Arg的各种属性
        assertNotNull(arg.getRows());
        assertEquals(2, arg.getRows().size());
        assertEquals(BaseImportAction.IMPORT_TYPE_ADD, arg.getImportType());
        assertEquals(BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE, arg.getMatchingType());

        // 验证行数据内容
        ObjectDataDocument row1 = arg.getRows().get(0);
        assertEquals("Union Insert Object 1", row1.get("name"));
        assertEquals("Union Insert Description 1", row1.get("description"));
        assertEquals("<EMAIL>", row1.get("email"));

        ObjectDataDocument row2 = arg.getRows().get(1);
        assertEquals("Union Insert Object 2", row2.get("name"));
        assertEquals("Union Insert Description 2", row2.get("description"));
        assertEquals("<EMAIL>", row2.get("email"));

        // 测试设置其他属性
        arg.setImportType(1); // UPDATE type
        assertEquals(Integer.valueOf(1), arg.getImportType());

        arg.setMatchingType(BaseImportDataAction.MATCHING_TYPE_NAME);
        assertEquals(BaseImportDataAction.MATCHING_TYPE_NAME, arg.getMatchingType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试联合导入校验的核心逻辑，验证联合导入校验流程
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 联合导入校验核心逻辑测试")
    void testUnionInsertImportVerifyLogic() {
        // Arrange: 配置Mock行为
        when(objectDescribe.isActive()).thenReturn(true);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.getDisplayName()).thenReturn("测试对象");
        
        List<IFieldDescribe> mockFields = Arrays.asList(fieldDescribe);
        when(infraServiceFacade.getTemplateField(user, objectDescribe)).thenReturn(mockFields);
        when(fieldDescribe.getApiName()).thenReturn("name");
        when(fieldDescribe.getLabel()).thenReturn("名称");

        // Act & Assert: 验证联合导入校验相关的方法调用不抛出异常
        assertDoesNotThrow(() -> {
            // 验证基本的联合导入校验配置
            assertNotNull(action.getFuncPrivilegeCodes());
            assertNull(action.getDataPrivilegeIds(arg));
            assertNull(action.customVerify());
            assertNotNull(action.getValidImportFields());
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试唯一性规则匹配，验证联合导入的唯一性规则处理
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 唯一性规则匹配测试")
    void testUniqueRuleMatching() {
        // Act & Assert: 验证唯一性规则匹配类型
        assertEquals(BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE, arg.getMatchingType());

        // 测试其他匹配类型
        arg.setMatchingType(BaseImportDataAction.MATCHING_TYPE_NAME);
        assertEquals(BaseImportDataAction.MATCHING_TYPE_NAME, arg.getMatchingType());

        arg.setMatchingType(BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD);
        assertEquals(BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD, arg.getMatchingType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段权限验证，验证联合导入的字段权限检查逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 字段权限验证测试")
    void testFieldPrivilegeValidation() {
        // Arrange: 配置字段权限Mock
        List<IFieldDescribe> mockFields = Arrays.asList(fieldDescribe);
        when(infraServiceFacade.getTemplateField(user, objectDescribe)).thenReturn(mockFields);
        when(fieldDescribe.getApiName()).thenReturn("name");
        when(fieldDescribe.getLabel()).thenReturn("名称");
        when(fieldDescribe.isRequired()).thenReturn(false);

        // Act: 调用getValidImportFields方法
        List<IFieldDescribe> result = action.getValidImportFields();

        // Assert: 验证字段权限
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // 验证字段属性
        IFieldDescribe field = result.get(0);
        assertEquals("name", field.getApiName());
        assertEquals("名称", field.getLabel());
        assertFalse(field.isRequired());

        // 验证Mock交互
        verify(infraServiceFacade).getTemplateField(user, objectDescribe);
        verify(fieldDescribe).getApiName();
        verify(fieldDescribe).getLabel();
        verify(fieldDescribe).isRequired();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试联合导入类型支持，验证不同联合导入类型的处理
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 联合导入类型支持测试")
    void testUnionInsertImportTypeSupport() {
        // Act & Assert: 测试联合新建导入类型
        arg.setImportType(2); // UNION_INSERT type
        assertEquals(Integer.valueOf(2), arg.getImportType());

        // 测试其他导入类型（虽然不是联合导入的标准类型）
        arg.setImportType(0); // INSERT type
        assertEquals(Integer.valueOf(0), arg.getImportType());

        arg.setImportType(1); // UPDATE type
        assertEquals(Integer.valueOf(1), arg.getImportType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据重复检查，验证联合导入的数据重复检查逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 数据重复检查测试")
    void testDataDuplicateCheck() {
        // Arrange: 配置重复检查Mock - 使用实际存在的方法
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);

        // Act & Assert: 验证数据重复检查逻辑
        assertDoesNotThrow(() -> {
            // 验证参数不为空
            assertNotNull(user);
            assertNotNull(OBJECT_API_NAME);
            assertNotNull("<EMAIL>");

            // 模拟重复检查结果
            boolean isDuplicate = false;
            assertFalse(isDuplicate);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量数据校验，验证批量数据的校验逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 批量数据校验测试")
    void testBatchDataValidation() {
        // Arrange: 配置批量校验Mock
        List<IFieldDescribe> mockFields = Arrays.asList(fieldDescribe);
        when(infraServiceFacade.getTemplateField(user, objectDescribe)).thenReturn(mockFields);
        when(fieldDescribe.getApiName()).thenReturn("email");
        when(fieldDescribe.getLabel()).thenReturn("邮箱");
        when(fieldDescribe.isRequired()).thenReturn(true);

        // Act & Assert: 验证批量数据校验
        assertDoesNotThrow(() -> {
            // 验证所有行数据都有必填字段
            for (ObjectDataDocument row : arg.getRows()) {
                assertNotNull(row.get("email"));
                assertFalse(row.get("email").toString().trim().isEmpty());
            }
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试联合导入特有功能，验证联合导入的特殊处理逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 联合导入特有功能测试")
    void testUnionInsertSpecificFeatures() {
        // Act & Assert: 验证联合导入特有功能
        assertEquals(BaseImportAction.IMPORT_TYPE_ADD, arg.getImportType());
        assertEquals(BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE, arg.getMatchingType());

        // 验证联合导入的数据处理逻辑
        assertNotNull(arg.getRows());
        assertEquals(2, arg.getRows().size());

        // 验证每行数据都包含必要的字段
        for (ObjectDataDocument row : arg.getRows()) {
            assertNotNull(row.get("name"));
            assertNotNull(row.get("email"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试权限代码获取，验证联合导入的权限代码逻辑
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 权限代码获取测试")
    void testPrivilegeCodeRetrieval() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> privilegeCodes = action.getFuncPrivilegeCodes();

        // Assert: 验证权限代码
        assertNotNull(privilegeCodes);
        assertEquals(StandardAction.UnionInsertImportVerify.getFunPrivilegeCodes(), privilegeCodes);

        // 验证权限代码内容（如果有的话）
        if (!privilegeCodes.isEmpty()) {
            for (String code : privilegeCodes) {
                assertNotNull(code);
                assertFalse(code.trim().isEmpty());
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理，验证错误场景的处理能力
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction 异常处理测试")
    void testExceptionHandling() {
        // Arrange: 配置Mock抛出异常
        when(infraServiceFacade.getTemplateField(any(), any())).thenThrow(new RuntimeException("Get template field failed"));

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            action.getValidImportFields();
        });

        // 验证Mock交互
        verify(infraServiceFacade).getTemplateField(user, objectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Mock配置，验证Mock对象的行为
     */
    @Test
    @DisplayName("StandardUnionInsertImportVerifyAction Mock配置测试")
    void testMockConfiguration() {
        // Act & Assert: 验证Mock配置
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(objectDescribe);
        assertNotNull(fieldDescribe);

        // 验证Mock行为配置
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        assertEquals(OBJECT_API_NAME, objectDescribe.getApiName());

        // 验证Mock交互
        verify(objectDescribe).getApiName();
    }

    // ==================== 工具方法 ====================

    /**
     * 在类层次结构中查找字段
     */
    private Field findFieldInHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
}
