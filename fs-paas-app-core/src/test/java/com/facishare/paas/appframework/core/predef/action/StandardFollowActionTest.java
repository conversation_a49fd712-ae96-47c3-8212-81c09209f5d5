package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.ObjectFollowInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;
import java.lang.reflect.Field;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardFollowAction的JUnit 5测试类
 * 测试标准关注Action的核心功能
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StandardFollowActionTest {

    // 测试数据常量
    private static final String TENANT_ID = "test_tenant";
    private static final String USER_ID = "test_user";
    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String ACTION_CODE = "Follow";
    private static final String OBJECT_ID = "test_object_id";

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IObjectData objectData;

    @Mock
    private FollowLogicService followLogicService;

    private StandardFollowAction action;
    private ActionContext actionContext;
    private User user;
    private RequestContext requestContext;
    private StandardFollowAction.Arg arg;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 初始化参数
        arg = StandardFollowAction.Arg.of(OBJECT_ID);

        // 初始化被测试对象
        action = new StandardFollowAction();
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(action, "arg", arg);
        
        // 设置数据列表
        List<IObjectData> dataList = Arrays.asList(objectData);
        Whitebox.setInternalState(action, "dataList", dataList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardFollowAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardFollowAction 基本功能测试")
    void testBasicFunctionality() {
        // Act & Assert: 验证对象初始化
        assertNotNull(action);
        assertNotNull(Whitebox.getInternalState(action, "serviceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "infraServiceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "actionContext"));
        assertNotNull(Whitebox.getInternalState(action, "objectDescribe"));
        assertNotNull(Whitebox.getInternalState(action, "arg"));

        // 验证基本属性
        ActionContext ctx = Whitebox.getInternalState(action, "actionContext");
        assertEquals(TENANT_ID, ctx.getTenantId());
        assertEquals(OBJECT_API_NAME, ctx.getObjectApiName());
        assertEquals(ACTION_CODE, ctx.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法，验证功能权限代码获取逻辑
     */
    @Test
    @DisplayName("StandardFollowAction getFuncPrivilegeCodes - 功能权限代码获取测试")
    void testGetFuncPrivilegeCodes() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> result = action.getFuncPrivilegeCodes();

        // Assert: 验证结果（关注操作不需要特殊权限）
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法，验证数据权限ID获取逻辑
     */
    @Test
    @DisplayName("StandardFollowAction getDataPrivilegeIds - 数据权限ID获取测试")
    void testGetDataPrivilegeIds() {
        // Act: 调用getDataPrivilegeIds方法
        List<String> result = action.getDataPrivilegeIds(arg);

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(OBJECT_ID, result.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonApiName方法，验证按钮API名称获取逻辑
     */
    @Test
    @DisplayName("StandardFollowAction getButtonApiName - 按钮API名称获取测试")
    void testGetButtonApiName() {
        // Act: 调用getButtonApiName方法
        String result = action.getButtonApiName();

        // Assert: 验证结果
        assertEquals(ObjectAction.FOLLOW.getButtonApiName(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的各种属性，验证参数类的功能
     */
    @Test
    @DisplayName("StandardFollowAction Arg参数类测试")
    void testArgClass() {
        // Act & Assert: 验证Arg的各种属性
        assertEquals(OBJECT_ID, arg.getObjectDataId());

        // 测试静态工厂方法
        StandardFollowAction.Arg factoryArg = StandardFollowAction.Arg.of("new_object_id");
        assertEquals("new_object_id", factoryArg.getObjectDataId());

        // 测试构造函数
        StandardFollowAction.Arg constructorArg = new StandardFollowAction.Arg("constructor_id");
        assertEquals("constructor_id", constructorArg.getObjectDataId());

        // 测试无参构造函数
        StandardFollowAction.Arg noArgsArg = new StandardFollowAction.Arg();
        assertNull(noArgsArg.getObjectDataId());
        
        // 设置对象数据ID
        noArgsArg.setObjectDataId("set_id");
        assertEquals("set_id", noArgsArg.getObjectDataId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类，验证结果对象的功能
     */
    @Test
    @DisplayName("StandardFollowAction Result结果类测试")
    void testResultClass() {
        // Arrange: 创建Result对象
        StandardFollowAction.Result result = new StandardFollowAction.Result();

        // Act & Assert: 验证Result对象（空结果类）
        assertNotNull(result);
        // Result类是空的，主要验证对象创建成功
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法，验证初始化逻辑
     */
    @Test
    @DisplayName("StandardFollowAction init - 初始化测试")
    void testInit() {
        // Arrange: 配置Mock行为
        when(objectData.getId()).thenReturn(OBJECT_ID);

        // 配置serviceFacade.findObjectDataByIdsExcludeInvalid Mock返回
        List<IObjectData> mockDataList = Arrays.asList(objectData);
        when(serviceFacade.findObjectDataByIdsExcludeInvalid(any(), any(), any())).thenReturn(mockDataList);

        // Act: 调用init方法
        action.init();

        // Assert: 验证初始化结果
        IObjectData resultObjectData = Whitebox.getInternalState(action, "objectData");
        assertNotNull(resultObjectData);
        assertEquals(OBJECT_ID, resultObjectData.getId());

        // 验证Mock交互 - getId()会被调用多次（在lambda中也会调用）
        verify(objectData, atLeastOnce()).getId();
        verify(serviceFacade).findObjectDataByIdsExcludeInvalid(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPreObjectData方法，验证前置对象数据获取逻辑
     */
    @Test
    @DisplayName("StandardFollowAction getPreObjectData - 前置对象数据获取测试")
    void testGetPreObjectData() {
        // Arrange: 设置对象数据 - 修复null返回值错误
        // 需要调用init()方法来初始化objectData字段，或者直接设置objectData字段
        when(objectData.getId()).thenReturn(OBJECT_ID);
        Whitebox.setInternalState(action, "objectData", objectData);

        // Act: 调用getPreObjectData方法
        IObjectData result = action.getPreObjectData();

        // Assert: 验证结果
        assertEquals(objectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPostObjectData方法，验证后置对象数据获取逻辑
     */
    @Test
    @DisplayName("StandardFollowAction getPostObjectData - 后置对象数据获取测试")
    void testGetPostObjectData() {
        // Arrange: 设置对象数据 - 修复null返回值错误
        // 需要调用init()方法来初始化objectData字段，或者直接设置objectData字段
        when(objectData.getId()).thenReturn(OBJECT_ID);
        Whitebox.setInternalState(action, "objectData", objectData);

        // Act: 调用getPostObjectData方法
        IObjectData result = action.getPostObjectData();

        // Assert: 验证结果
        assertEquals(objectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法，验证关注操作的核心逻辑
     */
    @Test
    @DisplayName("StandardFollowAction doAct - 关注操作核心逻辑测试")
    void testDoAct() {
        // Arrange: 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.getDisplayName()).thenReturn("测试对象");
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.getName()).thenReturn("Test Object Name");
        doNothing().when(followLogicService).bulkCreateFollowDataByUser(any(), any(), any(), any());

        // Act: 调用doAct方法
        StandardFollowAction.Result result = action.doAct(arg);

        // Assert: 验证结果
        assertNotNull(result);

        // 验证Mock交互 - 修复TooManyActualInvocations错误
        verify(serviceFacade).getBean(FollowLogicService.class);
        // objectDescribe.getApiName()在doAct方法中被调用2次（第61行和第71行），所以验证2次调用
        verify(objectDescribe, times(2)).getApiName();
        verify(objectDescribe).getDisplayName();
        verify(objectData).getId();
        verify(objectData).getName();
        verify(followLogicService).bulkCreateFollowDataByUser(eq(user), eq(ObjectFollowInfo.OBJECT_BIZ), eq(OBJECT_API_NAME), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的空参数处理，验证空对象ID的处理逻辑
     */
    @Test
    @DisplayName("StandardFollowAction doAct - 空参数处理测试")
    void testDoActWithBlankObjectId() {
        // Arrange: 设置空的对象ID
        StandardFollowAction.Arg blankArg = StandardFollowAction.Arg.of("");

        // Act: 调用doAct方法
        StandardFollowAction.Result result = action.doAct(blankArg);

        // Assert: 验证结果（空参数应该返回空结果）
        assertNotNull(result);

        // 验证没有调用关注服务
        verify(followLogicService, never()).bulkCreateFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试关注信息构建，验证ObjectFollowInfo的构建逻辑
     */
    @Test
    @DisplayName("StandardFollowAction 关注信息构建测试")
    void testObjectFollowInfoBuilding() {
        // Arrange: 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.getDisplayName()).thenReturn("测试对象");
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.getName()).thenReturn("Test Object Name");

        // 捕获传递给followLogicService的参数
        doAnswer(invocation -> {
            List<ObjectFollowInfo> followInfoList = invocation.getArgument(3);
            
            // 验证关注信息构建
            assertNotNull(followInfoList);
            assertEquals(1, followInfoList.size());
            
            ObjectFollowInfo followInfo = followInfoList.get(0);
            assertEquals(OBJECT_ID, followInfo.getFollowId());
            assertEquals("Test Object Name", followInfo.getFollowName());
            assertEquals(OBJECT_API_NAME, followInfo.getApiName());
            assertEquals("测试对象", followInfo.getLabel());
            assertEquals(ObjectFollowInfo.OBJECT_BIZ, followInfo.getBiz());
            
            return null;
        }).when(followLogicService).bulkCreateFollowDataByUser(any(), any(), any(), any());

        // Act: 调用doAct方法
        action.doAct(arg);

        // Assert: 验证Mock交互
        verify(followLogicService).bulkCreateFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量关注支持，验证多个对象的关注逻辑
     */
    @Test
    @DisplayName("StandardFollowAction 批量关注支持测试")
    void testBulkFollowSupport() {
        // Arrange: 准备多个对象数据
        IObjectData objectData2 = new ObjectData();
        objectData2.set("_id", "object_id_2");
        objectData2.set("name", "Test Object 2");

        IObjectData objectData3 = new ObjectData();
        objectData3.set("_id", "object_id_3");
        objectData3.set("name", "Test Object 3");

        List<IObjectData> multipleDataList = Arrays.asList(objectData, objectData2, objectData3);
        Whitebox.setInternalState(action, "dataList", multipleDataList);

        // 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.getDisplayName()).thenReturn("测试对象");
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.getName()).thenReturn("Test Object 1");

        // 捕获传递给followLogicService的参数
        doAnswer(invocation -> {
            List<ObjectFollowInfo> followInfoList = invocation.getArgument(3);
            
            // 验证批量关注信息
            assertNotNull(followInfoList);
            assertEquals(3, followInfoList.size());
            
            return null;
        }).when(followLogicService).bulkCreateFollowDataByUser(any(), any(), any(), any());

        // Act: 调用doAct方法
        action.doAct(arg);

        // Assert: 验证Mock交互
        verify(followLogicService).bulkCreateFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理，验证错误场景的处理能力
     */
    @Test
    @DisplayName("StandardFollowAction 异常处理测试")
    void testExceptionHandling() {
        // Arrange: 配置Mock抛出异常
        when(serviceFacade.getBean(FollowLogicService.class)).thenThrow(new RuntimeException("Follow service failed"));

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            action.doAct(arg);
        });

        // 验证Mock交互
        verify(serviceFacade).getBean(FollowLogicService.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Mock配置，验证Mock对象的行为
     */
    @Test
    @DisplayName("StandardFollowAction Mock配置测试")
    void testMockConfiguration() {
        // Act & Assert: 验证Mock配置
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(objectDescribe);
        assertNotNull(objectData);
        assertNotNull(followLogicService);

        // 验证Mock行为配置
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        assertEquals(OBJECT_API_NAME, objectDescribe.getApiName());

        // 验证Mock交互
        verify(objectDescribe).getApiName();
    }
}
