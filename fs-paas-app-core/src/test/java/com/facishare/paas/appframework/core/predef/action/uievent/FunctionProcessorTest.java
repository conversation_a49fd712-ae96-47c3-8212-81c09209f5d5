package com.facishare.paas.appframework.core.predef.action.uievent;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.FuncBizExtendParam;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fxiaoke.functions.utils.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionProcessorTest {

    @Mock
    private RequestContext requestContext;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private StandardTriggerEventAction action;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private IUIEvent event;
    
    @Mock
    private IUdefFunction function;
    
    private FunctionProcessor functionProcessor;

    private final String dataJson = "{\"name\":\"00001\",\"receivable_amount\":\"100.00\",\"select_option\":\"1\",\"multi_select_option\":[\"1\",\"2\"]}";

    @BeforeEach
    void setUp() {
        // 设置action的serviceFacade和infraServiceFacade
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);

        // 准备测试数据
        ObjectDataDocument objectDataDoc = ObjectDataDocument.of(JSON.parseObject(dataJson));

        // 使用标准方式构造IObjectDescribe
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("SalesOrderObj");

        // 构造字段描述 - 使用HashMap避免不可变Map的问题
        Map<String, Object> nameFieldMap = new HashMap<>();
        nameFieldMap.put("api_name", "name");
        nameFieldMap.put("type", IFieldType.AUTO_NUMBER);
        IFieldDescribe nameField = FieldDescribeFactory.newInstance(nameFieldMap);

        Map<String, Object> amountFieldMap = new HashMap<>();
        amountFieldMap.put("api_name", "receivable_amount");
        amountFieldMap.put("type", IFieldType.CURRENCY);
        IFieldDescribe amountField = FieldDescribeFactory.newInstance(amountFieldMap);

        Map<String, Object> selectOptionFieldMap = new HashMap<>();
        selectOptionFieldMap.put("api_name", "select_option");
        selectOptionFieldMap.put("type", IFieldType.SELECT_ONE);
        Map<String, Object> option1 = Maps.newHashMap();
        option1.put("value", "1");
        option1.put("label", "Option 1");
        Map<String, Object> option2 = Maps.newHashMap();
        option2.put("value", "2");
        option2.put("label", "Option 2");
        selectOptionFieldMap.put("options", Lists.newArrayList(option1, option2));
        IFieldDescribe selectOptionField = FieldDescribeFactory.newInstance(selectOptionFieldMap);
        Map<String, Object> multiSelectFieldMap = new HashMap<>();
        multiSelectFieldMap.put("api_name", "multi_select_option");
        multiSelectFieldMap.put("type", IFieldType.SELECT_MANY);
        Map<String, Object> multiOption1 = Maps.newHashMap();
        multiOption1.put("value", "1");
        multiOption1.put("label", "Multi Option 1");
        Map<String, Object> multiOption2 = Maps.newHashMap();
        multiOption2.put("value", "2");
        multiOption2.put("label", "Multi Option 2");
        Map<String, Object> multiOption3 = Maps.newHashMap();
        multiOption3.put("value", "3");
        multiOption3.put("label", "Multi Option 3");
        multiSelectFieldMap.put("options", Lists.newArrayList(multiOption1, multiOption2, multiOption3));
        IFieldDescribe multiSelectField = FieldDescribeFactory.newInstance(multiSelectFieldMap);

        objectDescribe.setFieldDescribes(Lists.newArrayList(nameField, amountField, selectOptionField, multiSelectField));

        // 配置action的ActionContainer接口方法 - 使用lenient模式
        lenient().when(action.getServiceFacade()).thenReturn(serviceFacade);
        lenient().when(action.getInfraServiceFacade()).thenReturn(infraServiceFacade);
        lenient().when(action.getContext()).thenReturn(requestContext);
        lenient().when(action.getMasterData()).thenReturn(objectDataDoc.toObjectData());
        lenient().when(action.getDetailData()).thenReturn(Collections.emptyMap());
        lenient().when(action.getObjectDescribe()).thenReturn(objectDescribe);
        lenient().when(action.getDetailDescribe()).thenReturn(Collections.emptyMap());

        // 创建FunctionProcessor实例
        functionProcessor = new FunctionProcessor(action);

        // 通过Whitebox设置私有字段
        Whitebox.setInternalState(functionProcessor, "event", event);
        Whitebox.setInternalState(functionProcessor, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(functionProcessor, "detailObjectDescribeMap", Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的正常执行流程，函数执行成功的场景
     */
    @Test
    @DisplayName("正常场景 - invoke方法执行成功")
    void testInvoke_SuccessfulExecution() {
        // 准备测试数据
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // 配置UdobjGrayConfig
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(true);

            // 配置AppFrameworkConfig
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.afterUiEventDoCalculateDetailsLastTime(any())).thenReturn(true);

            // 准备对象数据
            ObjectDataDocument objectDataDoc = ObjectDataDocument.of(JSON.parseObject(dataJson));

            // 构建ProcessRequest
            UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                    .masterData(objectDataDoc.toObjectData())
                    .masterWithOnlyChangedFields(ObjectDataDocument.of(Collections.singletonMap("field__c", "value")).toObjectData())
                    .doCalculate(true)
                    .detailDataMap(Collections.emptyMap())
                    .detailWithOnlyChangedFields(Collections.emptyMap())
                    .build();

            // 配置RunResult
            RunResult runResult = new RunResult();
            runResult.setSuccess(true);
            Map<String, Object> functionResult = new HashMap<>();
            functionResult.put("objectData", Collections.singletonMap("receivable_amount", "200.00"));
            functionResult.put("details", Collections.emptyMap());
            runResult.setFunctionResult(functionResult);

            // 配置Mock行为
            when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
            when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
            when(function.isActive()).thenReturn(true);
            when(function.getReturnType()).thenReturn("UIEvent");

            // 使用lenient模式避免严格参数匹配
            lenient().when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class), any(Map.class),
                    any(), any(Map.class), isNull(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

            User systemUser = User.systemUser("74255");
            when(requestContext.getUser()).thenReturn(systemUser);

            // 执行被测试方法
            assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

            // 验证Mock交互
            verify(serviceFacade, times(2)).getFunctionLogicService();
            verify(functionLogicService).findUDefFunction(any(), any(), any());
            verify(function).isActive();
            verify(functionLogicService).executeUDefFunction(any(User.class), any(IUdefFunction.class),
                    any(Map.class), any(), any(Map.class), isNull(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当函数不存在或不活跃时的处理逻辑
     */
    @Test
    @DisplayName("边界场景 - 函数不存在或不活跃")
    void testInvoke_InactiveFunction() {
        // 准备测试数据
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();
        
        // 配置Mock行为 - 函数不存在
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(null);
        
        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);
        
        // 执行被测试方法 - 应该直接返回不抛异常
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));
        
        // 验证Mock交互
        verify(serviceFacade).getFunctionLogicService();
        verify(functionLogicService).findUDefFunction(any(), any(), any());
        // 函数不存在时不应该执行后续逻辑
        verify(functionLogicService, never()).executeUDefFunction(any(User.class), any(IUdefFunction.class), 
                any(Map.class), any(), any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当函数执行失败时抛出异常
     */
    @Test
    @DisplayName("异常场景 - 函数执行失败")
    void testInvokeThrowsFunctionException_FunctionExecutionFailed() {
        // 准备测试数据
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();

        // 配置失败的RunResult
        RunResult runResult = new RunResult();
        runResult.setSuccess(false);
        runResult.setErrorInfo("Function execution failed");

        // Mock I18N静态方法
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            mockedI18N.when(() -> I18N.text(any())).thenReturn("Function execution error: Function execution failed");

            // 配置Mock行为 - 使用lenient模式避免参数匹配问题
            lenient().when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
            lenient().when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
            lenient().when(function.isActive()).thenReturn(true);
            lenient().when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                    any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

            User systemUser = User.systemUser("74255");
            lenient().when(requestContext.getUser()).thenReturn(systemUser);

            // 执行并验证异常 - 应该抛出FunctionException
            FunctionException exception = assertThrows(FunctionException.class, () ->
                functionProcessor.invoke(processRequest, mock(ProcessorContext.class))
            );

            // 验证异常信息包含预期内容
            assertNotNull(exception.getMessage());
            assertTrue(exception.getMessage().contains("Function execution failed"));

            // 验证Mock交互
            verify(functionLogicService).executeUDefFunction(any(User.class), any(IUdefFunction.class),
                    any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class));
        }
    }

    /**
     * 测试函数返回类型为Remind的场景
     */
    @Test
    @DisplayName("特殊场景 - 函数返回类型为Remind")
    void testInvoke_RemindReturnType() {
        // 准备测试数据
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();

        // 配置成功的RunResult，但返回类型为Remind
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map<String, Object> functionResult = new HashMap<>();
        functionResult.put("remind", Collections.singletonMap("content", "This is a remind message"));
        runResult.setFunctionResult(functionResult);

        // 配置Mock行为
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
        when(function.isActive()).thenReturn(true);
        when(function.getReturnType()).thenReturn("Remind"); // 关键：返回类型为Remind
        when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);

        ProcessorContext context = mock(ProcessorContext.class);

        // 执行被测试方法
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, context));

        // 验证context.setStop()被调用
        verify(context).setStop();

        // 验证request.setRemindEvent(true)被设置
        assertTrue(processRequest.isRemindEvent());
    }

    /**
     * 测试包含详情数据的场景
     */
    @Test
    @DisplayName("正常场景 - 包含详情数据处理")
    void testInvoke_WithDetailData() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // 配置灰度开关
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(true);
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.afterUiEventDoCalculateDetailsLastTime(any())).thenReturn(true);

            // 准备详情数据
            Map<String, List<IObjectData>> detailDataMap = new HashMap<>();
            List<IObjectData> detailList = Collections.singletonList(
                    ObjectDataDocument.of(Collections.singletonMap("detail_field", "detail_value")).toObjectData()
            );
            detailDataMap.put("DetailObj", detailList);

            UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                    .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                    .detailDataMap(detailDataMap)
                    .doCalculate(true)
                    .build();

            // 配置RunResult包含详情数据
            RunResult runResult = new RunResult();
            runResult.setSuccess(true);
            Map<String, Object> functionResult = new HashMap<>();
            functionResult.put("objectData", Collections.singletonMap("receivable_amount", "300.00"));

            Map<String, List<Map<String, Object>>> details = new HashMap<>();
            List<Map<String, Object>> detailResultList = Collections.singletonList(
                    Collections.singletonMap("detail_result_field", "detail_result_value")
            );
            details.put("DetailObj", detailResultList);
            functionResult.put("details", details);
            runResult.setFunctionResult(functionResult);

            // 配置Mock行为
            when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
            when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
            when(function.isActive()).thenReturn(true);
            when(function.getReturnType()).thenReturn("UIEvent");
            when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                    any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

            User systemUser = User.systemUser("74255");
            when(requestContext.getUser()).thenReturn(systemUser);

            // 执行被测试方法
            assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

            // 验证详情数据被正确处理
            assertNotNull(processRequest.getDetailDataMap());
            assertTrue(processRequest.isDoCalculate());
        }
    }

    /**
     * 测试needDoCalculate方法的不同分支
     */
    @Test
    @DisplayName("边界场景 - needDoCalculate方法测试")
    void testInvoke_NeedDoCalculateBranches() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                    .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                    .detailDataMap(Collections.emptyMap())
                    .build();

            // 配置RunResult，其中doCalculate为null，需要通过函数创建时间判断
            RunResult runResult = new RunResult();
            runResult.setSuccess(true);
            Map<String, Object> functionResult = new HashMap<>();
            functionResult.put("objectData", Collections.singletonMap("receivable_amount", "400.00"));
            functionResult.put("details", Collections.emptyMap()); // 添加空的details避免NPE
            functionResult.put("doCalculate", null); // 关键：doCalculate为null
            runResult.setFunctionResult(functionResult);

            // 配置AppFrameworkConfig返回false
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.afterUiEventDoCalculateDetailsLastTime(any())).thenReturn(false);

            // 配置Mock行为
            when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
            when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
            when(function.isActive()).thenReturn(true);
            when(function.getReturnType()).thenReturn("UIEvent");
            when(function.getCreateTime()).thenReturn(System.currentTimeMillis());
            when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                    any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

            User systemUser = User.systemUser("74255");
            when(requestContext.getUser()).thenReturn(systemUser);

            // 执行被测试方法
            assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

            // 验证doCalculate被设置为false（因为AppFrameworkConfig返回false）
            assertFalse(processRequest.isDoCalculate());
        }
    }

    /**
     * 测试函数不活跃的场景
     */
    @Test
    @DisplayName("边界场景 - 函数不活跃")
    void testInvoke_InactiveFunction_IsActiveFalse() {
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();

        // 配置Mock行为 - 函数存在但不活跃
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
        when(function.isActive()).thenReturn(false); // 关键：函数不活跃

        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);

        // 执行被测试方法 - 应该直接返回不抛异常
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

        // 验证Mock交互
        verify(serviceFacade).getFunctionLogicService();
        verify(functionLogicService).findUDefFunction(any(), any(), any());
        verify(function).isActive();
        // 函数不活跃时不应该执行后续逻辑
        verify(functionLogicService, never()).executeUDefFunction(any(User.class), any(IUdefFunction.class),
                any(Map.class), any(), any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
    }

    /**
     * 测试包含选项属性的场景
     */
    @Test
    @DisplayName("正常场景 - 包含选项属性处理")
    void testInvoke_WithOptionAttribute() {
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();

        // 配置RunResult包含选项属性
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map<String, Object> functionResult = new HashMap<>();
        Map<String, Object> objectData = new HashMap<>();
        objectData.put("receivable_amount", "500.00");
        objectData.put("select_option", "1");
        objectData.put("multi_select_option", Lists.newArrayList("1", "2"));
        functionResult.put("objectData", objectData);
        functionResult.put("details", Collections.emptyMap()); // 添加空的details避免NPE

        // 添加选项属性
        Map<String, Map<String, Object>> optionAttribute = new HashMap<>();
        Map<String, Object> fieldOptions = new HashMap<>();
        Map<String, Object> optionConfig = new HashMap<>();
        Map<String, Boolean> hiddenConfig = new HashMap<>();
        hiddenConfig.put("hidden", true);
        optionConfig.put("1", hiddenConfig);
        fieldOptions.put("select_option", optionConfig);
        Map<String, Object> multiOptionConfig = new HashMap<>();
        multiOptionConfig.put("1", hiddenConfig);
        multiOptionConfig.put("2", hiddenConfig);
        fieldOptions.put("multi_select_option", multiOptionConfig);
        optionAttribute.put("SalesOrderObj", fieldOptions);
        functionResult.put("optionAttribute", optionAttribute);

        runResult.setFunctionResult(functionResult);

        // 配置Mock行为
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
        when(function.isActive()).thenReturn(true);
        when(function.getReturnType()).thenReturn("UIEvent");
        when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);

        // 执行被测试方法
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

        // 验证选项属性被正确设置
        assertNotNull(processRequest.getOptionAttribute());
        assertEquals(optionAttribute, processRequest.getOptionAttribute());
    }

    /**
     * 测试needDoCalculate方法中doCalculate不为null的分支
     */
    @Test
    @DisplayName("边界场景 - needDoCalculate方法doCalculate不为null")
    void testInvoke_NeedDoCalculateWithNonNullValue() {
        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(Collections.emptyMap())
                .build();

        // 配置RunResult，其中doCalculate为true
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map<String, Object> functionResult = new HashMap<>();
        functionResult.put("objectData", Collections.singletonMap("receivable_amount", "600.00"));
        functionResult.put("details", Collections.emptyMap());
        functionResult.put("doCalculate", true); // 关键：doCalculate为true
        runResult.setFunctionResult(functionResult);

        // 配置Mock行为
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
        when(function.isActive()).thenReturn(true);
        when(function.getReturnType()).thenReturn("UIEvent");
        when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);

        // 执行被测试方法
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

        // 验证doCalculate被设置为true（直接从函数结果返回）
        assertTrue(processRequest.isDoCalculate());
    }

    /**
     * 测试包含详情对象描述的场景，覆盖validateObjectData和correctFieldValue的详情处理逻辑
     */
    @Test
    @DisplayName("正常场景 - 包含详情对象描述的数据处理")
    void testInvoke_WithDetailObjectDescribe() {
        // 准备包含详情数据的请求
        Map<String, List<IObjectData>> detailDataMap = new HashMap<>();
        List<IObjectData> detailList = Collections.singletonList(
                ObjectDataDocument.of(Collections.singletonMap("detail_field", "detail_value")).toObjectData()
        );
        detailDataMap.put("DetailObj", detailList);

        UIEventProcess.ProcessRequest processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(ObjectDataDocument.of(JSON.parseObject(dataJson)).toObjectData())
                .detailDataMap(detailDataMap)
                .build();

        // 配置RunResult
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map<String, Object> functionResult = new HashMap<>();
        functionResult.put("objectData", Collections.singletonMap("receivable_amount", "700.00"));
        functionResult.put("details", Collections.emptyMap());
        runResult.setFunctionResult(functionResult);

        // 配置Mock行为
        when(serviceFacade.getFunctionLogicService()).thenReturn(functionLogicService);
        when(functionLogicService.findUDefFunction(any(), any(), any())).thenReturn(function);
        when(function.isActive()).thenReturn(true);
        when(function.getReturnType()).thenReturn("UIEvent");
        when(functionLogicService.executeUDefFunction(any(User.class), any(IUdefFunction.class),
                any(Map.class), any(), any(Map.class), any(), any(Map.class), any(FuncBizExtendParam.Arg.class))).thenReturn(runResult);

        User systemUser = User.systemUser("74255");
        when(requestContext.getUser()).thenReturn(systemUser);

        // 模拟detailObjectDescribeMap不为空
        Map<String, IObjectDescribe> detailObjectDescribeMap = new HashMap<>();
        IObjectDescribe detailDescribe = mock(IObjectDescribe.class);
        detailObjectDescribeMap.put("DetailObj", detailDescribe);
        Whitebox.setInternalState(functionProcessor, "detailObjectDescribeMap", detailObjectDescribeMap);

        // 执行被测试方法
        assertDoesNotThrow(() -> functionProcessor.invoke(processRequest, mock(ProcessorContext.class)));

        // 验证详情数据被处理
        assertNotNull(processRequest.getDetailDataMap());
    }


}