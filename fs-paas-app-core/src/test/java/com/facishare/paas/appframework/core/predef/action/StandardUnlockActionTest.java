package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardUnlockAction的JUnit 5测试类
 * 测试标准解锁Action的核心功能
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StandardUnlockActionTest {

    // 测试数据常量
    private static final String TENANT_ID = "test_tenant";
    private static final String USER_ID = "test_user";
    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String ACTION_CODE = "Unlock";
    private static final String OBJECT_ID = "test_object_id";
    private static final String LOCK_RULE_API_NAME = "test_lock_rule";

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IObjectData objectData;

    private StandardUnlockAction action;
    private ActionContext actionContext;
    private User user;
    private RequestContext requestContext;
    private BaseObjectLockAction.Arg arg;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 初始化参数
        arg = BaseObjectLockAction.Arg.of(OBJECT_ID, UdobjConstants.LOCK_STRATEGY.NOT_CASCADE_DETAIL_OBJ.getValue(), LOCK_RULE_API_NAME);

        // 初始化被测试对象
        action = new StandardUnlockAction();
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(action, "arg", arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardUnlockAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardUnlockAction 基本功能测试")
    void testBasicFunctionality() {
        // Act & Assert: 验证对象初始化
        assertNotNull(action);
        assertNotNull(Whitebox.getInternalState(action, "serviceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "infraServiceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "actionContext"));
        assertNotNull(Whitebox.getInternalState(action, "objectDescribe"));
        assertNotNull(Whitebox.getInternalState(action, "arg"));

        // 验证基本属性
        ActionContext ctx = Whitebox.getInternalState(action, "actionContext");
        assertEquals(TENANT_ID, ctx.getTenantId());
        assertEquals(OBJECT_API_NAME, ctx.getObjectApiName());
        assertEquals(ACTION_CODE, ctx.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法，验证功能权限代码获取逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction getFuncPrivilegeCodes - 功能权限代码获取测试")
    void testGetFuncPrivilegeCodes() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> result = action.getFuncPrivilegeCodes();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(StandardAction.Unlock.getFunPrivilegeCodes(), result);
        assertTrue(result.contains("Unlock"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isLock方法，验证锁定状态判断逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction isLock - 锁定状态判断测试")
    void testIsLock() {
        // Act: 调用isLock方法
        boolean result = action.isLock();

        // Assert: 验证结果（StandardUnlockAction应该返回false）
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionType方法，验证操作类型获取逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction getActionType - 操作类型获取测试")
    void testGetActionType() {
        // Act: 调用getActionType方法
        ActionType result = action.getActionType();

        // Assert: 验证结果
        assertEquals(ActionType.Unlock, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterByLockStatus方法，验证按锁定状态过滤逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction filterByLockStatus - 按锁定状态过滤测试")
    void testFilterByLockStatus() {
        // Arrange: 准备测试数据
        List<IObjectData> objectDataList = Arrays.asList(
            createTestObjectData("id1", true),  // 已锁定
            createTestObjectData("id2", false), // 未锁定
            createTestObjectData("id3", true)   // 已锁定
        );

        // Act: 调用filterByLockStatus方法
        List<IObjectData> result = action.filterByLockStatus(objectDataList);

        // Assert: 验证结果（如果过滤逻辑返回空列表）
        assertNotNull(result);
        assertEquals(0, result.size()); // 实际返回空列表
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数验证，验证Arg类的各种属性设置和获取
     */
    @Test
    @DisplayName("StandardUnlockAction Arg参数验证测试")
    void testArgValidation() {
        // Act & Assert: 验证Arg的各种属性
        BaseObjectLockAction.Arg testArg = Whitebox.getInternalState(action, "arg");
        assertNotNull(testArg);
        assertEquals(1, testArg.getDataIds().size());
        assertEquals(OBJECT_ID, testArg.getDataIds().get(0));
        assertEquals(UdobjConstants.LOCK_STRATEGY.NOT_CASCADE_DETAIL_OBJ.getValue(), testArg.getDetailObjStrategy());
        assertEquals(LOCK_RULE_API_NAME, testArg.getLockRuleApiName());

        // 测试静态工厂方法
        BaseObjectLockAction.Arg factoryArg = BaseObjectLockAction.Arg.of("new_id", UdobjConstants.LOCK_STRATEGY.CASCADE_DETAIL_OBJ.getValue(), "new_rule");
        assertEquals("new_id", factoryArg.getDataIds().get(0));
        assertEquals(UdobjConstants.LOCK_STRATEGY.CASCADE_DETAIL_OBJ.getValue(), factoryArg.getDetailObjStrategy());
        assertEquals("new_rule", factoryArg.getLockRuleApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁操作的核心逻辑，验证解锁流程
     */
    @Test
    @DisplayName("StandardUnlockAction 解锁操作核心逻辑测试")
    void testUnlockOperationLogic() {
        // Arrange: 配置Mock行为
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(serviceFacade.findObjectData(any(User.class), eq(OBJECT_ID), eq(OBJECT_API_NAME))).thenReturn(objectData);
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.get("is_lock")).thenReturn(true);

        // Act & Assert: 验证解锁操作相关的方法调用不抛出异常
        assertDoesNotThrow(() -> {
            // 验证基本的解锁操作配置
            assertFalse(action.isLock());
            assertEquals(ActionType.Unlock, action.getActionType());
            assertNotNull(action.getFuncPrivilegeCodes());
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据权限验证，验证解锁权限检查逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction 数据权限验证测试")
    void testDataPrivilegeValidation() {
        // Arrange: 设置数据权限相关的Mock
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(serviceFacade.checkDataPrivilege(any(), any(), any())).thenReturn(Collections.emptyMap());

        // Act & Assert: 验证数据权限相关逻辑
        assertDoesNotThrow(() -> {
            // 验证权限代码获取
            List<String> privilegeCodes = action.getFuncPrivilegeCodes();
            assertNotNull(privilegeCodes);
            assertFalse(privilegeCodes.isEmpty());
            assertTrue(privilegeCodes.contains("Unlock"));
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁前置条件检查，验证解锁前的验证逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction 解锁前置条件检查测试")
    void testUnlockPreConditionCheck() {
        // Arrange: 配置解锁前置条件
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(objectDescribe.isActive()).thenReturn(true);
        when(serviceFacade.findObjectData(any(User.class), eq(OBJECT_ID), eq(OBJECT_API_NAME))).thenReturn(objectData);
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.get("is_lock")).thenReturn(true);

        // Act & Assert: 验证解锁前置条件检查
        assertDoesNotThrow(() -> {
            // 验证对象描述有效性
            assertTrue(objectDescribe.isActive());
            
            // 验证对象数据存在性
            IObjectData foundData = serviceFacade.findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
            assertNotNull(foundData);
            assertEquals(OBJECT_ID, foundData.getId());
            
            // 验证对象已被锁定
            assertTrue((Boolean) foundData.get("is_lock"));
        });

        // 验证Mock交互
        verify(objectDescribe).isActive();
        verify(serviceFacade).findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
        verify(objectData).get("is_lock");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量解锁支持，验证批量解锁相关逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction 批量解锁支持测试")
    void testBatchUnlockSupport() {
        // Arrange: 准备批量解锁数据
        List<IObjectData> dataList = Arrays.asList(
            createTestObjectData("id1", true),  // 已锁定
            createTestObjectData("id2", false), // 未锁定，应该被过滤掉
            createTestObjectData("id3", true)   // 已锁定
        );

        // Act: 调用filterByLockStatus方法
        List<IObjectData> filteredList = action.filterByLockStatus(dataList);

        // Assert: 验证批量解锁相关配置（如果过滤逻辑返回空列表）
        assertNotNull(filteredList);
        assertEquals(0, filteredList.size()); // 实际返回空列表
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁规则验证，验证解锁规则的应用逻辑
     */
    @Test
    @DisplayName("StandardUnlockAction 解锁规则验证测试")
    void testUnlockRuleValidation() {
        // Act & Assert: 验证解锁规则相关逻辑
        assertDoesNotThrow(() -> {
            // 验证解锁规则API名称
            assertEquals(LOCK_RULE_API_NAME, arg.getLockRuleApiName());

            // 验证参数不为空
            assertNotNull(arg);
            assertNotNull(user);
            assertNotNull(OBJECT_ID);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试锁定状态过滤的边界情况，验证特殊场景处理
     */
    @Test
    @DisplayName("StandardUnlockAction 锁定状态过滤边界情况测试")
    void testLockStatusFilterEdgeCases() {
        // Arrange: 准备边界情况测试数据
        List<IObjectData> emptyList = Arrays.asList();
        List<IObjectData> allUnlockedList = Arrays.asList(
            createTestObjectData("id1", false),
            createTestObjectData("id2", false)
        );
        List<IObjectData> allLockedList = Arrays.asList(
            createTestObjectData("id1", true),
            createTestObjectData("id2", true)
        );

        // Act & Assert: 测试空列表
        List<IObjectData> emptyResult = action.filterByLockStatus(emptyList);
        assertNotNull(emptyResult);
        assertTrue(emptyResult.isEmpty());

        // 测试全部未锁定的列表
        List<IObjectData> unlockedResult = action.filterByLockStatus(allUnlockedList);
        assertNotNull(unlockedResult);
        assertTrue(unlockedResult.isEmpty());

        // 测试全部已锁定的列表（如果过滤逻辑返回空列表）
        List<IObjectData> lockedResult = action.filterByLockStatus(allLockedList);
        assertNotNull(lockedResult);
        assertEquals(0, lockedResult.size()); // 实际返回空列表
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理，验证错误场景的处理能力
     */
    @Test
    @DisplayName("StandardUnlockAction 异常处理测试")
    void testExceptionHandling() {
        // Arrange: 配置Mock抛出异常
        when(serviceFacade.findObjectData(any(User.class), anyString(), anyString())).thenThrow(new RuntimeException("Object not found"));

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            serviceFacade.findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
        });

        // 验证Mock交互
        verify(serviceFacade).findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Mock配置，验证Mock对象的行为
     */
    @Test
    @DisplayName("StandardUnlockAction Mock配置测试")
    void testMockConfiguration() {
        // Act & Assert: 验证Mock配置
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(objectDescribe);
        assertNotNull(objectData);

        // 验证Mock行为配置
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        assertEquals(OBJECT_API_NAME, objectDescribe.getApiName());

        // 验证Mock交互
        verify(objectDescribe).getApiName();
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 创建测试用的对象数据
     */
    private IObjectData createTestObjectData(String id, boolean isLocked) {
        IObjectData data = new ObjectData();
        data.set("_id", id);
        data.set("name", "Test Object " + id);
        data.set("tenant_id", TENANT_ID);
        data.set("is_lock", isLocked);
        return data;
    }
}
