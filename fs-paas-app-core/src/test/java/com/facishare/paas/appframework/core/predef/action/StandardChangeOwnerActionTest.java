package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardChangeOwnerAction的JUnit 5测试类
 * 测试标准变更负责人Action的核心功能
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StandardChangeOwnerActionTest {

    // 测试数据常量
    private static final String TENANT_ID = "test_tenant";
    private static final String USER_ID = "test_user";
    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String ACTION_CODE = "ChangeOwner";
    private static final String OBJECT_ID = "test_object_id";
    private static final String NEW_OWNER_ID = "new_owner_id";
    private static final String OLD_OWNER_ID = "old_owner_id";

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IObjectData objectData;

    private StandardChangeOwnerAction action;
    private ActionContext actionContext;
    private User user;
    private RequestContext requestContext;
    private StandardChangeOwnerAction.Arg arg;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 初始化参数
        StandardChangeOwnerAction.ChangeOwnerData changeOwnerData = StandardChangeOwnerAction.ChangeOwnerData.builder()
                .objectDataId(OBJECT_ID)
                .ownerId(Arrays.asList(NEW_OWNER_ID))
                .dataOwnDepartmentId(Arrays.asList("dept_id"))
                .build();

        arg = StandardChangeOwnerAction.Arg.builder()
                .data(Arrays.asList(changeOwnerData))
                .oldOwnerStrategy("REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM")
                .isCascadeDealDetail(false)
                .skipTriggerApprovalFlow(false)
                .isUpdateDataOwnDepartment(true)
                .build();

        // 初始化被测试对象
        action = new StandardChangeOwnerAction();
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(action, "arg", arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardChangeOwnerAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 基本功能测试")
    void testBasicFunctionality() {
        // Act & Assert: 验证对象初始化
        assertNotNull(action);
        assertNotNull(Whitebox.getInternalState(action, "serviceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "infraServiceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "actionContext"));
        assertNotNull(Whitebox.getInternalState(action, "objectDescribe"));
        assertNotNull(Whitebox.getInternalState(action, "arg"));

        // 验证基本属性
        ActionContext ctx = Whitebox.getInternalState(action, "actionContext");
        assertEquals(TENANT_ID, ctx.getTenantId());
        assertEquals(OBJECT_API_NAME, ctx.getObjectApiName());
        assertEquals(ACTION_CODE, ctx.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法，验证功能权限代码获取逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction getFuncPrivilegeCodes - 功能权限代码获取测试")
    void testGetFuncPrivilegeCodes() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> result = action.getFuncPrivilegeCodes();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(StandardAction.ChangeOwner.getFunPrivilegeCodes(), result);
        assertTrue(result.contains("ChangeOwner"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的各种属性，验证参数类的功能
     */
    @Test
    @DisplayName("StandardChangeOwnerAction Arg参数类测试")
    void testArgClass() {
        // Act & Assert: 验证Arg的各种属性
        assertNotNull(arg.getData());
        assertEquals(1, arg.getData().size());
        assertEquals(OBJECT_ID, arg.getData().get(0).getObjectDataId());
        assertEquals(NEW_OWNER_ID, arg.getData().get(0).getOwnerId().get(0));
        assertEquals("REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM", arg.getOldOwnerStrategy());
        assertFalse(arg.isCascadeDealDetail());
        assertFalse(arg.isSkipTriggerApprovalFlow());
        assertTrue(arg.isUpdateDataOwnDepartment());

        // 测试设置其他属性
        arg.setOldOwnerTeamMemberRole("MEMBER");
        assertEquals("MEMBER", arg.getOldOwnerTeamMemberRole());

        arg.setOldOwnerTeamMemberRoleList(Arrays.asList("ADMIN", "MEMBER"));
        assertEquals(2, arg.getOldOwnerTeamMemberRoleList().size());

        arg.setRelateObjectApiNames(Arrays.asList("RelatedObject1__c", "RelatedObject2__c"));
        assertEquals(2, arg.getRelateObjectApiNames().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ChangeOwnerData类，验证变更负责人数据对象的功能
     */
    @Test
    @DisplayName("StandardChangeOwnerAction ChangeOwnerData数据类测试")
    void testChangeOwnerDataClass() {
        // Arrange: 创建ChangeOwnerData对象
        StandardChangeOwnerAction.ChangeOwnerData changeOwnerData = StandardChangeOwnerAction.ChangeOwnerData.builder()
                .objectDataId("test_id")
                .ownerId(Arrays.asList("owner1", "owner2"))
                .dataOwnDepartmentId(Arrays.asList("dept1", "dept2"))
                .dataOwnOrganizationId(Arrays.asList("org1"))
                .build();

        // Act & Assert: 验证ChangeOwnerData属性
        assertEquals("test_id", changeOwnerData.getObjectDataId());
        assertEquals(2, changeOwnerData.getOwnerId().size());
        assertTrue(changeOwnerData.getOwnerId().contains("owner1"));
        assertTrue(changeOwnerData.getOwnerId().contains("owner2"));
        assertEquals(2, changeOwnerData.getDataOwnDepartmentId().size());
        assertEquals(1, changeOwnerData.getDataOwnOrganizationId().size());
        assertEquals("org1", changeOwnerData.getDataOwnOrganizationId().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类，验证结果对象的功能
     */
    @Test
    @DisplayName("StandardChangeOwnerAction Result结果类测试")
    void testResultClass() {
        // Arrange: 创建Result对象
        BaseObjectSaveAction.ValidationMessage validationMessage = BaseObjectSaveAction.ValidationMessage.builder()
                .isMatch(true)
                .build();

        StandardChangeOwnerAction.Result result = StandardChangeOwnerAction.Result.builder()
                .errorCode("0")
                .message("变更负责人成功")
                .validationMessage(validationMessage)
                .build();

        // Act & Assert: 验证Result属性
        assertEquals("0", result.getErrorCode());
        assertEquals("变更负责人成功", result.getMessage());
        assertNotNull(result.getValidationMessage());
        assertTrue(result.getValidationMessage().isMatch());

        // 测试属性修改
        result.setErrorCode("1");
        result.setMessage("变更负责人失败");
        
        assertEquals("1", result.getErrorCode());
        assertEquals("变更负责人失败", result.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试变更负责人操作的核心逻辑，验证变更流程
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 变更负责人操作核心逻辑测试")
    void testChangeOwnerOperationLogic() {
        // Arrange: 配置Mock行为
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        when(serviceFacade.findObjectData(any(User.class), eq(OBJECT_ID), eq(OBJECT_API_NAME))).thenReturn(objectData);
        when(objectData.getId()).thenReturn(OBJECT_ID);
        when(objectData.get("owner_id")).thenReturn(OLD_OWNER_ID);

        // Act & Assert: 验证变更负责人操作相关的方法调用不抛出异常
        assertDoesNotThrow(() -> {
            // 验证基本的变更负责人操作配置
            assertNotNull(action.getFuncPrivilegeCodes());
            assertEquals(OBJECT_ID, arg.getData().get(0).getObjectDataId());
            assertEquals(NEW_OWNER_ID, arg.getData().get(0).getOwnerId().get(0));
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试级联处理详情对象，验证级联变更逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 级联处理详情对象测试")
    void testCascadeDealDetail() {
        // Arrange: 设置级联处理详情对象
        arg.setCascadeDealDetail(true);

        // Act & Assert: 验证级联处理配置
        assertTrue(arg.isCascadeDealDetail());

        // 验证级联处理时的特殊逻辑
        assertDoesNotThrow(() -> {
            // 级联处理时应该有不同的处理逻辑
            assertTrue(arg.isCascadeDealDetail());
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试旧负责人策略，验证不同策略的处理逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 旧负责人策略测试")
    void testOldOwnerStrategy() {
        // Act & Assert: 测试移除原负责人策略
        arg.setOldOwnerStrategy("REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM");
        assertEquals("REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM", arg.getOldOwnerStrategy());

        // 测试保留原负责人策略
        arg.setOldOwnerStrategy("KEEP_ORIGINAL_OWNER_IN_RELEVANT_TEAM");
        assertEquals("KEEP_ORIGINAL_OWNER_IN_RELEVANT_TEAM", arg.getOldOwnerStrategy());

        // 测试添加为团队成员策略
        arg.setOldOwnerStrategy("ADD_ORIGINAL_OWNER_AS_TEAM_MEMBER");
        assertEquals("ADD_ORIGINAL_OWNER_AS_TEAM_MEMBER", arg.getOldOwnerStrategy());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试团队成员角色配置，验证团队角色的设置逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 团队成员角色配置测试")
    void testTeamMemberRoleConfiguration() {
        // Arrange: 设置团队成员角色
        arg.setOldOwnerTeamMemberRole("ADMIN");
        arg.setOldOwnerTeamMemberRoleList(Arrays.asList("ADMIN", "MEMBER", "VIEWER"));
        arg.setOldOwnerTeamMemberPermissionType("READ_WRITE");

        // Act & Assert: 验证团队成员角色配置
        assertEquals("ADMIN", arg.getOldOwnerTeamMemberRole());
        assertEquals(3, arg.getOldOwnerTeamMemberRoleList().size());
        assertTrue(arg.getOldOwnerTeamMemberRoleList().contains("ADMIN"));
        assertTrue(arg.getOldOwnerTeamMemberRoleList().contains("MEMBER"));
        assertTrue(arg.getOldOwnerTeamMemberRoleList().contains("VIEWER"));
        assertEquals("READ_WRITE", arg.getOldOwnerTeamMemberPermissionType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试归属部门和组织更新，验证归属关系的变更逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 归属部门和组织更新测试")
    void testDataOwnDepartmentAndOrganizationUpdate() {
        // Act & Assert: 测试归属部门更新
        assertTrue(arg.isUpdateDataOwnDepartment());
        
        // 测试不更新归属部门
        arg.setUpdateDataOwnDepartment(false);
        assertFalse(arg.isUpdateDataOwnDepartment());

        // 测试归属组织更新
        arg.setUpdateDataOwnOrganization(true);
        assertTrue(arg.isUpdateDataOwnOrganization());

        // 验证ChangeOwnerData中的归属信息
        StandardChangeOwnerAction.ChangeOwnerData data = arg.getData().get(0);
        assertNotNull(data.getDataOwnDepartmentId());
        assertNotNull(data.getDataOwnOrganizationId());
        assertFalse(data.getDataOwnDepartmentId().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试相关对象处理，验证关联对象的负责人变更
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 相关对象处理测试")
    void testRelatedObjectHandling() {
        // Arrange: 设置相关对象API名称
        List<String> relateObjectApiNames = Arrays.asList("Contact__c", "Opportunity__c", "Task__c");
        arg.setRelateObjectApiNames(relateObjectApiNames);

        // Act & Assert: 验证相关对象配置
        assertEquals(3, arg.getRelateObjectApiNames().size());
        assertTrue(arg.getRelateObjectApiNames().contains("Contact__c"));
        assertTrue(arg.getRelateObjectApiNames().contains("Opportunity__c"));
        assertTrue(arg.getRelateObjectApiNames().contains("Task__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试审批流跳过配置，验证审批流的跳过逻辑
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 审批流跳过配置测试")
    void testSkipTriggerApprovalFlow() {
        // Act & Assert: 测试不跳过审批流
        assertFalse(arg.isSkipTriggerApprovalFlow());

        // 测试跳过审批流
        arg.setSkipTriggerApprovalFlow(true);
        assertTrue(arg.isSkipTriggerApprovalFlow());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理，验证错误场景的处理能力
     */
    @Test
    @DisplayName("StandardChangeOwnerAction 异常处理测试")
    void testExceptionHandling() {
        // Arrange: 配置Mock抛出异常
        when(serviceFacade.findObjectData(any(User.class), any(String.class), any(String.class))).thenThrow(new RuntimeException("Object not found"));

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            serviceFacade.findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
        });

        // 验证Mock交互
        verify(serviceFacade).findObjectData(user, OBJECT_ID, OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Mock配置，验证Mock对象的行为
     */
    @Test
    @DisplayName("StandardChangeOwnerAction Mock配置测试")
    void testMockConfiguration() {
        // Act & Assert: 验证Mock配置
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(objectDescribe);
        assertNotNull(objectData);

        // 验证Mock行为配置
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        assertEquals(OBJECT_API_NAME, objectDescribe.getApiName());

        // 验证Mock交互
        verify(objectDescribe).getApiName();
    }
}
