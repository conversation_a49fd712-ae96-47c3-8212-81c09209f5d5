package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringContextUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;
import java.lang.reflect.Field;
import org.springframework.context.ApplicationContext;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardAddTeamMemberAction的JUnit 5测试类
 * 迁移自StandardAddTeamMemberActionGroovyTest.groovy
 */
@ExtendWith(MockitoExtension.class)
class StandardAddTeamMemberActionTest {

    // 测试数据常量
    private static final String OBJECT_API_NAME = "object_123__c";
    private static final String ACTION_CODE = "AddTeamMember";
    private static final String TENANT_ID = "590064";
    private static final String USER_ID = "1000";

    private static final String ARG_JSON = "{\"teamMemberInfos\":[{\"teamMemberEmployee\":[\"1078\",\"1095\",\"1034\"],\"teamMemberPermissionType\":\"1\",\"teamMemberRole\":\"\",\"teamMemberRoleList\":[\"4\"],\"teamMemberType\":0,\"outTeamMemberEmployee\":[]},{\"teamMemberEmployee\":[\"1160\"],\"teamMemberPermissionType\":\"1\",\"teamMemberRole\":\"\",\"teamMemberRoleList\":[\"4\",\"1\"],\"teamMemberType\":0,\"outTeamMemberEmployee\":[]},{\"teamMemberEmployee\":[],\"teamMemberPermissionType\":\"1\",\"teamMemberRole\":\"\",\"teamMemberRoleList\":[\"4\",\"2\"],\"teamMemberType\":0,\"outTeamMemberEmployee\":[{\"userId\":\"100148709\",\"outTenantId\":\"200158833\"}]}],\"otherObjects\":[],\"dataIDs\":[\"65a9147f75961b0001bfee81\"]}";

    private static final String DATA_JSON = "{\"field_C2H9o__c\":\"示111例文本\",\"field_fhm3V__c__r\":\"北京市\",\"field_247l3__c\":\"示例文本\",\"mc_exchange_rate\":\"123.000000\",\"field_Rj6uF__c\":1705544460000,\"owner_department_id\":\"1123\",\"field_33igZ__c__r\":\"中国\",\"data_own_department__r\":{\"deptName\":\"第三方平台主属部门\",\"deptId\":\"1123\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"version\":\"9\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1123\",\"deptName\":\"第三方平台主属部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_cNmsG__c\":\"示例文本\",\"data_own_organization\":[\"999999\"],\"field_Z6h36__c\":\"1.00\",\"field_tW638__c\":\"249\",\"data_own_organization__l\":[{\"deptId\":\"999999\",\"deptName\":\"基础业务集成测试087(590064)\",\"status\":0,\"deptType\":\"org\"}],\"currency_field_22wm3__c\":{\"prefix\":\"￥\",\"suffix\":\"元\",\"fieldApiName\":\"currency_field_22wm3__c\",\"objectApiName\":\"object_m50x5__c\"},\"field_vL3au__c\":\"13935\",\"field_9tzp7__c\":\"0#%$0#%$定位信息\",\"field_vL3au__c__r\":\"中关村街道\",\"field_DBJo1__c\":\"PD5qjvdlX\",\"field_22wm3__c__r\":\"￥ 123.00 元\",\"data_own_organization__r\":{\"deptName\":\"基础业务集成测试087(590064)\",\"deptId\":\"999999\",\"deptType\":\"org\",\"status\":0},\"field_g4kYw__c\":[\"1002\"],\"field_nrmpN__c\":\"PD5qjvdlX\",\"last_modified_time\":1721029395706,\"life_status\":\"normal\",\"field_8Z844__c\":8460000,\"out_tenant_id\":\"301875149\",\"field_33igZ__c\":\"248\",\"field_Fb8qy__c__p\":{\"mobile\":\"13900000000\",\"province\":\"新疆维吾尔自治区\",\"city\":\"乌鲁木齐市\",\"code\":\"0991\",\"operator\":\"移动\",\"mobilePath\":\"新疆维吾尔自治区乌鲁木齐市 移动\",\"i18n\":{\"province\":\"新疆维吾尔自治区\",\"city\":\"乌鲁木齐市\",\"operator\":\"移动\"}},\"field_2QiUE__c\":\"635\",\"field_1Sbpb__c\":\"示例文本\",\"out_owner__r\":{\"dept\":\"301875149\",\"name\":\"zeng\",\"nickname\":\"zeng\",\"tenantId\":\"590064\",\"id\":\"311044105\",\"enterpriseName\":\"yxtest1.ui\",\"status\":0},\"field_F7s1A__c\":[],\"out_owner__l\":[{\"id\":\"311044105\",\"tenantId\":\"590064\",\"enterpriseName\":\"yxtest1.ui\",\"name\":\"zeng\",\"nickname\":\"zeng\",\"status\":0,\"dept\":\"301875149\"}],\"created_by__r\":{\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"mobile\":\"12289607222\",\"description\":\"\",\"dept\":\"1123\",\"empNum\":\"1234\",\"modifyTime\":1719823563828,\"post\":\"测试\",\"createTime\":1527665433600,\"phone\":\"43567890\",\"name\":\"lihh李\",\"nickname\":\"lihh李\",\"tenantId\":\"590064\",\"id\":\"1002\",\"email\":\"<EMAIL>\",\"status\":0},\"field_wEUzb__c\":\"123.00\",\"field_6G683__c\":[],\"owner_department\":\"第三方平台主属部门\",\"field_g4kYw__c__r\":{\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"mobile\":\"12289607222\",\"description\":\"\",\"dept\":\"1123\",\"empNum\":\"1234\",\"modifyTime\":1719823563828,\"post\":\"测试\",\"createTime\":1527665433600,\"phone\":\"43567890\",\"name\":\"lihh李\",\"nickname\":\"lihh李\",\"tenantId\":\"590064\",\"id\":\"1002\",\"email\":\"<EMAIL>\",\"status\":0},\"field_g4kYw__c__l\":[{\"id\":\"1002\",\"tenantId\":\"590064\",\"name\":\"lihh李\",\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"email\":\"<EMAIL>\",\"nickname\":\"lihh李\",\"phone\":\"43567890\",\"description\":\"\",\"status\":0,\"createTime\":1527665433600,\"modifyTime\":1719823563828,\"dept\":\"1123\",\"post\":\"测试\",\"empNum\":\"1234\"}],\"field_7kvdy__c\":1705507200000,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1705579647553,\"field_x6aP4__c\":\"<p>富文本信息ww</p>\",\"field_x6aP4__c__o\":\"富文本信息ww\",\"created_by\":[\"1002\"],\"relevant_team\":[{\"innerDepartmentMember\":false,\"deptCascaded\":false,\"innerEmployeeMember\":true,\"outTeamMember\":false,\"teamMemberEmployee\":[\"1160\"],\"teamMemberRole\":\"1\",\"teamMemberRoleList\":[\"1\"],\"teamMemberPermissionType\":\"2\",\"outTenantId\":\"\",\"sourceType\":\"\",\"teamMemberType\":\"0\",\"teamMemberDeptCascade\":\"0\"},{\"innerDepartmentMember\":false,\"deptCascaded\":false,\"innerEmployeeMember\":true,\"outTeamMember\":true,\"teamMemberEmployee\":[\"311044105\"],\"teamMemberRole\":\"1\",\"teamMemberRoleList\":[\"1\"],\"teamMemberPermissionType\":\"2\",\"outTenantId\":\"301875149\",\"outUserName\":\"zeng\",\"sourceType\":\"2\",\"teamMemberType\":\"0\",\"teamMemberDeptCascade\":\"0\"}],\"field_drb1A__c\":\"dsdsd111\",\"field_mxc04__c\":\"<EMAIL>\",\"data_own_department\":[\"1123\"],\"field_2ou6A__c\":\"123.00\",\"name\":\"311121\",\"field_32rRI__c\":\"示例文本\",\"_id\":\"65a9147f75961b0001bfee81\",\"field_tW638__c__r\":\"北京市\",\"is_deleted\":false,\"field_8l27O__c\":\"14\",\"field_fd5cR__c\":\"示例文本\\n23232\\n343\",\"object_describe_api_name\":\"object_m50x5__c\",\"owner__l\":[{\"id\":\"1160\",\"tenantId\":\"590064\",\"name\":\"韩萌\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"韩萌\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1715255222567,\"modifyTime\":1718953080632,\"dept\":\"1123\",\"post\":\"\",\"empNum\":\"韩萌\"}],\"out_owner\":[\"311044105\"],\"field_Fb8qy__c\":\"13900000000\",\"field_8Daco__c\":\"1\",\"owner__r\":{\"picAddr\":\"\",\"mobile\":\"17082237758\",\"description\":\"\",\"dept\":\"1123\",\"empNum\":\"韩萌\",\"modifyTime\":1718953080632,\"post\":\"\",\"createTime\":1715255222567,\"phone\":\"\",\"name\":\"韩萌\",\"nickname\":\"韩萌\",\"tenantId\":\"590064\",\"id\":\"1160\",\"email\":\"\",\"status\":0},\"owner\":[\"1160\"],\"field_wGfey__c\":1705544460000,\"field_46n95__c\":\"sHb7f772n\",\"last_modified_by__l\":[{\"id\":\"1002\",\"tenantId\":\"590064\",\"name\":\"lihh李\",\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"email\":\"<EMAIL>\",\"nickname\":\"lihh李\",\"phone\":\"43567890\",\"description\":\"\",\"status\":0,\"createTime\":1527665433600,\"modifyTime\":1719823563828,\"dept\":\"1123\",\"post\":\"测试\",\"empNum\":\"1234\"}],\"created_by__l\":[{\"id\":\"1002\",\"tenantId\":\"590064\",\"name\":\"lihh李\",\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"email\":\"<EMAIL>\",\"nickname\":\"lihh李\",\"phone\":\"43567890\",\"description\":\"\",\"status\":0,\"createTime\":1527665433600,\"modifyTime\":1719823563828,\"dept\":\"1123\",\"post\":\"测试\",\"empNum\":\"1234\"}],\"last_modified_by\":[\"1002\"],\"field_2QiUE__c__r\":\"海淀区\",\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":\"N_202206_24_adaa32ccd4d4449ab61b35fa501215ff\",\"mobile\":\"12289607222\",\"description\":\"\",\"dept\":\"1123\",\"empNum\":\"1234\",\"modifyTime\":1719823563828,\"post\":\"测试\",\"createTime\":1527665433600,\"phone\":\"43567890\",\"name\":\"lihh李\",\"nickname\":\"lihh李\",\"tenantId\":\"590064\",\"id\":\"1002\",\"email\":\"<EMAIL>\",\"status\":0},\"field_22wm3__c\":\"123.00\",\"field_0jpRl__c\":1705544460000}";

    @Mock
    private ApplicationContext mockApplicationContext;

    private User user;
    private RequestContext requestContext;
    private ActionContext actionContext;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 设置SpringContextUtil的Mock
        try {
            Whitebox.setInternalState(SpringContextUtil.class, "CONTEXT", mockApplicationContext);
        } catch (Exception e) {
            // 如果设置失败，记录但不影响测试继续
            System.out.println("Warning: Failed to set SpringContextUtil mock: " + e.getMessage());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardAddTeamMemberAction的updateObjectDataByAddMember方法，验证团队成员添加功能
     * 迁移自Groovy测试：def "updateObjectDataByAddMemberTest"()
     */
    @Test
    @DisplayName("StandardAddTeamMemberAction updateObjectDataByAddMember - 添加团队成员更新对象数据")
    void testUpdateObjectDataByAddMember() {
        // Arrange: 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(DATA_JSON);
        
        StandardAddTeamMemberAction.Arg actionArg = JSON.parseObject(ARG_JSON, StandardAddTeamMemberAction.Arg.class);
        List<TeamMemberInfoPoJo> teamMemberInfos = actionArg.getTeamMemberInfos();
        actionArg.setAddTeamMemberRole(true);

        StandardAddTeamMemberAction action = new StandardAddTeamMemberAction();
        action.setActionContext(actionContext);
        action.setArg(actionArg);

        // Act & Assert: 执行测试并验证不抛出异常
        assertDoesNotThrow(() -> {
            action.updateObjectDataByAddMember(teamMemberInfos, objectData);
        });

        // 验证对象数据的基本属性
        assertNotNull(objectData);
        assertEquals("65a9147f75961b0001bfee81", objectData.get("_id"));
        assertEquals("object_m50x5__c", objectData.get("object_describe_api_name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardAddTeamMemberAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardAddTeamMemberAction 基本功能测试")
    void testBasicFunctionality() {
        // Arrange & Act: 创建Action实例
        StandardAddTeamMemberAction action = new StandardAddTeamMemberAction();
        action.setActionContext(actionContext);

        // Assert: 验证基本属性
        assertNotNull(action);
        assertNotNull(action.getActionContext());
        assertEquals(TENANT_ID, action.getActionContext().getTenantId());
        assertEquals(OBJECT_API_NAME, action.getActionContext().getObjectApiName());
        assertEquals(ACTION_CODE, action.getActionContext().getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试团队成员信息的JSON解析，验证参数解析功能
     */
    @Test
    @DisplayName("StandardAddTeamMemberAction 团队成员信息JSON解析测试")
    void testTeamMemberInfoJsonParsing() {
        // Act: 解析JSON参数
        assertDoesNotThrow(() -> {
            StandardAddTeamMemberAction.Arg actionArg = JSON.parseObject(ARG_JSON, StandardAddTeamMemberAction.Arg.class);
            
            // Assert: 验证解析结果
            assertNotNull(actionArg);
            assertNotNull(actionArg.getTeamMemberInfos());
            assertEquals(3, actionArg.getTeamMemberInfos().size());
            
            // 验证第一个团队成员信息
            TeamMemberInfoPoJo firstMember = actionArg.getTeamMemberInfos().get(0);
            assertNotNull(firstMember);
            assertEquals(3, firstMember.getTeamMemberEmployee().size());
            assertTrue(firstMember.getTeamMemberEmployee().contains("1078"));
            assertTrue(firstMember.getTeamMemberEmployee().contains("1095"));
            assertTrue(firstMember.getTeamMemberEmployee().contains("1034"));
            
            // 验证数据ID
            assertNotNull(actionArg.getDataIDs());
            assertEquals(1, actionArg.getDataIDs().size());
            assertEquals("65a9147f75961b0001bfee81", actionArg.getDataIDs().get(0));
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据的JSON解析，验证复杂对象数据处理
     */
    @Test
    @DisplayName("StandardAddTeamMemberAction 对象数据JSON解析测试")
    void testObjectDataJsonParsing() {
        // Act & Assert: 解析对象数据JSON
        assertDoesNotThrow(() -> {
            IObjectData objectData = new ObjectData();
            objectData.fromJsonString(DATA_JSON);
            
            // 验证基本字段 - 修复expected: <590064> but was: <null>错误
            assertNotNull(objectData);
            assertEquals("65a9147f75961b0001bfee81", objectData.get("_id"));
            assertEquals("object_m50x5__c", objectData.get("object_describe_api_name"));
            // 修复tenant_id断言 - JSON中没有tenant_id字段，但owner__r中有tenantId
            assertNotNull(objectData.get("owner__r"));
            assertEquals("311121", objectData.get("name"));
            
            // 验证复杂字段
            assertNotNull(objectData.get("relevant_team"));
            assertNotNull(objectData.get("data_own_department__r"));
            assertNotNull(objectData.get("owner__r"));
        });
    }

    // ==================== 工具方法 ====================

    /**
     * 在类层次结构中查找字段
     */
    private Field findFieldInHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
}
