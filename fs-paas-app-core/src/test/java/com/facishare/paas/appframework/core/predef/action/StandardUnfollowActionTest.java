package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.ObjectFollowInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.reflect.Whitebox;
import java.lang.reflect.Field;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardUnfollowAction的JUnit 5测试类
 * 测试标准取消关注Action的核心功能
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StandardUnfollowActionTest {

    // 测试数据常量
    private static final String TENANT_ID = "test_tenant";
    private static final String USER_ID = "test_user";
    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String ACTION_CODE = "Unfollow";
    private static final String OBJECT_ID = "test_object_id";

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private InfraServiceFacade infraServiceFacade;

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IObjectData objectData;

    @Mock
    private FollowLogicService followLogicService;

    private StandardUnfollowAction action;
    private ActionContext actionContext;
    private User user;
    private RequestContext requestContext;
    private StandardUnfollowAction.Arg arg;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        user = User.builder().tenantId(TENANT_ID).userId(USER_ID).build();
        requestContext = RequestContext.builder().tenantId(TENANT_ID).user(user).build();
        actionContext = new ActionContext(requestContext, OBJECT_API_NAME, ACTION_CODE);

        // 初始化参数
        arg = StandardUnfollowAction.Arg.of(OBJECT_ID);

        // 初始化被测试对象
        action = new StandardUnfollowAction();
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(action, "arg", arg);
        
        // 设置数据列表
        List<IObjectData> dataList = Arrays.asList(objectData);
        Whitebox.setInternalState(action, "dataList", dataList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试StandardUnfollowAction的基本功能，验证对象初始化和配置
     */
    @Test
    @DisplayName("StandardUnfollowAction 基本功能测试")
    void testBasicFunctionality() {
        // Act & Assert: 验证对象初始化
        assertNotNull(action);
        assertNotNull(Whitebox.getInternalState(action, "serviceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "infraServiceFacade"));
        assertNotNull(Whitebox.getInternalState(action, "actionContext"));
        assertNotNull(Whitebox.getInternalState(action, "objectDescribe"));
        assertNotNull(Whitebox.getInternalState(action, "arg"));

        // 验证基本属性
        ActionContext ctx = Whitebox.getInternalState(action, "actionContext");
        assertEquals(TENANT_ID, ctx.getTenantId());
        assertEquals(OBJECT_API_NAME, ctx.getObjectApiName());
        assertEquals(ACTION_CODE, ctx.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法，验证功能权限代码获取逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction getFuncPrivilegeCodes - 功能权限代码获取测试")
    void testGetFuncPrivilegeCodes() {
        // Act: 调用getFuncPrivilegeCodes方法
        List<String> result = action.getFuncPrivilegeCodes();

        // Assert: 验证结果（取消关注操作不需要特殊权限）
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法，验证数据权限ID获取逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction getDataPrivilegeIds - 数据权限ID获取测试")
    void testGetDataPrivilegeIds() {
        // Act: 调用getDataPrivilegeIds方法
        List<String> result = action.getDataPrivilegeIds(arg);

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(OBJECT_ID, result.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonApiName方法，验证按钮API名称获取逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction getButtonApiName - 按钮API名称获取测试")
    void testGetButtonApiName() {
        // Act: 调用getButtonApiName方法
        String result = action.getButtonApiName();

        // Assert: 验证结果
        assertEquals(ObjectAction.UNFOLLOW.getButtonApiName(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的各种属性，验证参数类的功能
     */
    @Test
    @DisplayName("StandardUnfollowAction Arg参数类测试")
    void testArgClass() {
        // Act & Assert: 验证Arg的各种属性
        assertEquals(OBJECT_ID, arg.getObjectDataId());

        // 测试静态工厂方法
        StandardUnfollowAction.Arg factoryArg = StandardUnfollowAction.Arg.of("new_object_id");
        assertEquals("new_object_id", factoryArg.getObjectDataId());

        // 测试构造函数
        StandardUnfollowAction.Arg constructorArg = new StandardUnfollowAction.Arg("constructor_id");
        assertEquals("constructor_id", constructorArg.getObjectDataId());

        // 测试无参构造函数
        StandardUnfollowAction.Arg noArgsArg = new StandardUnfollowAction.Arg();
        assertNull(noArgsArg.getObjectDataId());
        
        // 设置对象数据ID
        noArgsArg.setObjectDataId("set_id");
        assertEquals("set_id", noArgsArg.getObjectDataId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类，验证结果对象的功能
     */
    @Test
    @DisplayName("StandardUnfollowAction Result结果类测试")
    void testResultClass() {
        // Arrange: 创建Result对象
        StandardUnfollowAction.Result result = new StandardUnfollowAction.Result();

        // Act & Assert: 验证Result对象（空结果类）
        assertNotNull(result);
        // Result类是空的，主要验证对象创建成功
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法，验证初始化逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction init - 初始化测试")
    void testInit() {
        // Arrange: 配置Mock行为
        when(objectData.getId()).thenReturn(OBJECT_ID);

        // 配置serviceFacade.findObjectDataByIdsExcludeInvalid Mock返回
        List<IObjectData> mockDataList = Arrays.asList(objectData);
        when(serviceFacade.findObjectDataByIdsExcludeInvalid(any(), any(), any())).thenReturn(mockDataList);

        // Act: 调用init方法
        action.init();

        // Assert: 验证初始化结果
        IObjectData resultObjectData = Whitebox.getInternalState(action, "objectData");
        assertNotNull(resultObjectData);
        assertEquals(OBJECT_ID, resultObjectData.getId());

        // 验证Mock交互 - getId()会被调用多次（在lambda中也会调用）
        verify(objectData, atLeastOnce()).getId();
        verify(serviceFacade).findObjectDataByIdsExcludeInvalid(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPreObjectData方法，验证前置对象数据获取逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction getPreObjectData - 前置对象数据获取测试")
    void testGetPreObjectData() {
        // Arrange: 设置对象数据 - 修复null返回值错误
        // 需要调用init()方法来初始化objectData字段，或者直接设置objectData字段
        when(objectData.getId()).thenReturn(OBJECT_ID);
        Whitebox.setInternalState(action, "objectData", objectData);

        // Act: 调用getPreObjectData方法
        IObjectData result = action.getPreObjectData();

        // Assert: 验证结果
        assertEquals(objectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPostObjectData方法，验证后置对象数据获取逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction getPostObjectData - 后置对象数据获取测试")
    void testGetPostObjectData() {
        // Arrange: 设置对象数据 - 修复null返回值错误
        // 需要调用init()方法来初始化objectData字段，或者直接设置objectData字段
        when(objectData.getId()).thenReturn(OBJECT_ID);
        Whitebox.setInternalState(action, "objectData", objectData);

        // Act: 调用getPostObjectData方法
        IObjectData result = action.getPostObjectData();

        // Assert: 验证结果
        assertEquals(objectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法，验证取消关注操作的核心逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction doAct - 取消关注操作核心逻辑测试")
    void testDoAct() {
        // Arrange: 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        doNothing().when(followLogicService).deleteOneFollowDataByUser(any(), any(), any(), any());

        // Act: 调用doAct方法
        StandardUnfollowAction.Result result = action.doAct(arg);

        // Assert: 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(serviceFacade).getBean(FollowLogicService.class);
        verify(objectDescribe).getApiName();
        verify(followLogicService).deleteOneFollowDataByUser(user, ObjectFollowInfo.OBJECT_BIZ, OBJECT_API_NAME, OBJECT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的空参数处理，验证空对象ID的处理逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction doAct - 空参数处理测试")
    void testDoActWithBlankObjectId() {
        // Arrange: 设置空的对象ID
        StandardUnfollowAction.Arg blankArg = StandardUnfollowAction.Arg.of("");

        // Act: 调用doAct方法
        StandardUnfollowAction.Result result = action.doAct(blankArg);

        // Assert: 验证结果（空参数应该返回空结果）
        assertNotNull(result);

        // 验证没有调用取消关注服务
        verify(followLogicService, never()).deleteOneFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的null参数处理，验证null对象ID的处理逻辑
     */
    @Test
    @DisplayName("StandardUnfollowAction doAct - null参数处理测试")
    void testDoActWithNullObjectId() {
        // Arrange: 设置null的对象ID
        StandardUnfollowAction.Arg nullArg = StandardUnfollowAction.Arg.of(null);

        // Act: 调用doAct方法
        StandardUnfollowAction.Result result = action.doAct(nullArg);

        // Assert: 验证结果（null参数应该返回空结果）
        assertNotNull(result);

        // 验证没有调用取消关注服务
        verify(followLogicService, never()).deleteOneFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试取消关注的业务逻辑，验证FollowLogicService的调用
     */
    @Test
    @DisplayName("StandardUnfollowAction 取消关注业务逻辑测试")
    void testUnfollowBusinessLogic() {
        // Arrange: 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);

        // 验证deleteOneFollowDataByUser方法的调用参数
        doAnswer(invocation -> {
            User user = invocation.getArgument(0);
            String biz = invocation.getArgument(1);
            String apiName = invocation.getArgument(2);
            String objectId = invocation.getArgument(3);
            
            // 验证调用参数
            assertEquals(USER_ID, user.getUserId());
            assertEquals(TENANT_ID, user.getTenantId());
            assertEquals(ObjectFollowInfo.OBJECT_BIZ, biz);
            assertEquals(OBJECT_API_NAME, apiName);
            assertEquals(OBJECT_ID, objectId);
            
            return null;
        }).when(followLogicService).deleteOneFollowDataByUser(any(), any(), any(), any());

        // Act: 调用doAct方法
        action.doAct(arg);

        // Assert: 验证Mock交互
        verify(followLogicService).deleteOneFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试与StandardFollowAction的对比，验证相反操作的一致性
     */
    @Test
    @DisplayName("StandardUnfollowAction 与StandardFollowAction对比测试")
    void testComparisonWithFollowAction() {
        // Act & Assert: 验证取消关注与关注操作的一致性
        
        // 权限代码应该相同（都是空列表）
        List<String> unfollowPrivileges = action.getFuncPrivilegeCodes();
        assertTrue(unfollowPrivileges.isEmpty());
        
        // 按钮API名称应该不同
        assertEquals(ObjectAction.UNFOLLOW.getButtonApiName(), action.getButtonApiName());
        assertNotEquals(ObjectAction.FOLLOW.getButtonApiName(), action.getButtonApiName());
        
        // 数据权限ID获取逻辑应该相同
        List<String> dataPrivilegeIds = action.getDataPrivilegeIds(arg);
        assertEquals(1, dataPrivilegeIds.size());
        assertEquals(OBJECT_ID, dataPrivilegeIds.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数验证，验证不同参数值的处理
     */
    @Test
    @DisplayName("StandardUnfollowAction 参数验证测试")
    void testParameterValidation() {
        // Arrange: 准备不同的参数值
        String[] testIds = {"valid_id", "", null, "   ", "special@id#123"};

        for (String testId : testIds) {
            // Arrange: 创建测试参数
            StandardUnfollowAction.Arg testArg = StandardUnfollowAction.Arg.of(testId);
            
            // Act & Assert: 验证参数处理
            if (testId == null || testId.trim().isEmpty()) {
                // 空参数应该不调用服务
                action.doAct(testArg);
                verify(followLogicService, never()).deleteOneFollowDataByUser(any(), any(), any(), eq(testId));
            } else {
                // 有效参数应该正常处理
                when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
                when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
                
                assertDoesNotThrow(() -> action.doAct(testArg));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理，验证错误场景的处理能力
     */
    @Test
    @DisplayName("StandardUnfollowAction 异常处理测试")
    void testExceptionHandling() {
        // Arrange: 配置Mock抛出异常
        when(serviceFacade.getBean(FollowLogicService.class)).thenThrow(new RuntimeException("Unfollow service failed"));

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            action.doAct(arg);
        });

        // 验证Mock交互
        verify(serviceFacade).getBean(FollowLogicService.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FollowLogicService异常处理，验证服务层异常的处理
     */
    @Test
    @DisplayName("StandardUnfollowAction FollowLogicService异常处理测试")
    void testFollowLogicServiceExceptionHandling() {
        // Arrange: 配置Mock行为
        when(serviceFacade.getBean(FollowLogicService.class)).thenReturn(followLogicService);
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        doThrow(new RuntimeException("Delete follow data failed")).when(followLogicService)
                .deleteOneFollowDataByUser(any(), any(), any(), any());

        // Act & Assert: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            action.doAct(arg);
        });

        // 验证Mock交互
        verify(followLogicService).deleteOneFollowDataByUser(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Mock配置，验证Mock对象的行为
     */
    @Test
    @DisplayName("StandardUnfollowAction Mock配置测试")
    void testMockConfiguration() {
        // Act & Assert: 验证Mock配置
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(objectDescribe);
        assertNotNull(objectData);
        assertNotNull(followLogicService);

        // 验证Mock行为配置
        when(objectDescribe.getApiName()).thenReturn(OBJECT_API_NAME);
        assertEquals(OBJECT_API_NAME, objectDescribe.getApiName());

        // 验证Mock交互
        verify(objectDescribe).getApiName();
    }
}
