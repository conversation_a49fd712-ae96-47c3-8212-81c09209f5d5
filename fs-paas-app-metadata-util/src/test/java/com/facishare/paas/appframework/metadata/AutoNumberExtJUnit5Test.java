package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.AutoNumber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AutoNumberExt的JUnit 5测试类
 * 测试自增编号字段扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试自增编号字段扩展的创建、验证和比较功能
 */
class AutoNumberExtJUnit5Test {

    private AutoNumber mockAutoNumber;
    private AutoNumberExt autoNumberExt;

    @BeforeEach
    void setUp() {
        mockAutoNumber = mock(AutoNumber.class);
        autoNumberExt = AutoNumberExt.of(mockAutoNumber);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Arrange: 设置自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getLabel()).thenReturn("测试编号");
        
        // Act: 使用of方法创建实例
        AutoNumberExt result = AutoNumberExt.of(mockAutoNumber);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertEquals("test_number", result.getApiName());
        assertEquals("测试编号", result.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of方法传入null参数的异常情况
     */
    @Test
    @DisplayName("边界条件 - of方法传入null参数")
    void testOf_NullParameter() {
        // Act & Assert: 验证传入null参数会抛出异常
        AutoNumberExt result = AutoNumberExt.of(null);
        assertThrows(NullPointerException.class, () -> {
            result.getApiName();
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试autoNumberValidate静态方法能够正确验证自增编号
     */
    @ParameterizedTest
    @CsvSource({
        "NONE, TEST, END",
        "YEAR, PREFIX, ''",
        "MONTH, '', SUFFIX",
        "DAY, '', ''"
    })
    @DisplayName("静态方法 - autoNumberValidate方法验证有效自增编号")
    void testAutoNumberValidate_ValidAutoNumber(String condition, String prefix, String postfix) {
        // Arrange: 创建自增编号
        AutoNumber autoNumber = mock(AutoNumber.class);
        when(autoNumber.getApiName()).thenReturn("test_number");
        when(autoNumber.getLabel()).thenReturn("测试编号");
        when(autoNumber.getCondition()).thenReturn(condition);
        when(autoNumber.getPrefix()).thenReturn(prefix);
        when(autoNumber.getPostfix()).thenReturn(postfix);
        
        // Act & Assert: 验证不会抛出异常
        assertDoesNotThrow(() -> {
            AutoNumberExt.autoNumberValidate(Arrays.asList(autoNumber));
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试autoNumberValidate静态方法对无效条件的验证
     */
    @Test
    @DisplayName("静态方法 - autoNumberValidate方法验证无效条件")
    void testAutoNumberValidate_InvalidCondition() {
        // Arrange: 创建带有无效条件的自增编号
        AutoNumber autoNumber = mock(AutoNumber.class);
        when(autoNumber.getApiName()).thenReturn("test_number");
        when(autoNumber.getLabel()).thenReturn("测试编号");
        when(autoNumber.getCondition()).thenReturn("INVALID_CONDITION");
        when(autoNumber.getDescribeApiName()).thenReturn("test_object");

        // Act & Assert: 验证会抛出异常（由于RequestContext为null，会抛出NullPointerException）
        assertThrows(NullPointerException.class, () -> {
            AutoNumberExt.autoNumberValidate(Arrays.asList(autoNumber));
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试autoNumberValidate静态方法对空列表的处理
     */
    @Test
    @DisplayName("静态方法 - autoNumberValidate方法处理空列表")
    void testAutoNumberValidate_EmptyList() {
        // Act & Assert: 验证不会抛出异常
        assertDoesNotThrow(() -> {
            AutoNumberExt.autoNumberValidate(Arrays.asList());
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法能够正确比较相同自增编号
     */
    @Test
    @DisplayName("变更检测 - equals方法相同自增编号")
    void testEquals_SameAutoNumber() {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getCondition()).thenReturn("NONE");
        when(mockAutoNumber.getPrefix()).thenReturn("TEST");
        when(mockAutoNumber.getPostfix()).thenReturn("END");
        when(mockAutoNumber.getSerialNumber()).thenReturn(4);
        when(mockAutoNumber.getStartNumber()).thenReturn(1);
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);

        // 创建相同的自增编号
        AutoNumber sameAutoNumber = mock(AutoNumber.class);
        when(sameAutoNumber.getApiName()).thenReturn("test_number");
        when(sameAutoNumber.getCondition()).thenReturn("NONE");
        when(sameAutoNumber.getPrefix()).thenReturn("TEST");
        when(sameAutoNumber.getPostfix()).thenReturn("END");
        when(sameAutoNumber.getSerialNumber()).thenReturn(4);
        when(sameAutoNumber.getStartNumber()).thenReturn(1);
        when(sameAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);

        AutoNumberExt sameExt = AutoNumberExt.of(sameAutoNumber);

        // Act & Assert: 验证equals方法
        assertEquals(autoNumberExt, sameExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法能够正确比较不同自增编号
     */
    @Test
    @DisplayName("变更检测 - equals方法不同自增编号")
    void testEquals_DifferentAutoNumber() {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getCondition()).thenReturn("NONE");
        when(mockAutoNumber.getPrefix()).thenReturn("TEST");
        when(mockAutoNumber.getPostfix()).thenReturn("END");
        when(mockAutoNumber.getSerialNumber()).thenReturn(4);
        when(mockAutoNumber.getStartNumber()).thenReturn(1);
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);

        // 创建不同的自增编号
        AutoNumber differentAutoNumber = mock(AutoNumber.class);
        when(differentAutoNumber.getApiName()).thenReturn("test_number");
        when(differentAutoNumber.getCondition()).thenReturn("YEAR"); // 不同条件
        when(differentAutoNumber.getPrefix()).thenReturn("TEST");
        when(differentAutoNumber.getPostfix()).thenReturn("END");
        when(differentAutoNumber.getSerialNumber()).thenReturn(4);
        when(differentAutoNumber.getStartNumber()).thenReturn(1);
        when(differentAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);

        AutoNumberExt differentExt = AutoNumberExt.of(differentAutoNumber);

        // Act & Assert: 验证equals方法
        assertNotEquals(autoNumberExt, differentExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEffectiveImmediately方法能够正确判断是否立即生效
     */
    @ParameterizedTest
    @CsvSource({
        "OLD, OLD, 1, 4, false",
        "NEW, OLD, 1, 4, true",
        "OLD, NEW, 1, 4, true",
        "OLD, OLD, 2, 4, true",
        "OLD, OLD, 1, 5, true"
    })
    @DisplayName("立即生效 - isEffectiveImmediately方法")
    void testIsEffectiveImmediately(String newPrefix, String newPostfix, int newStartNumber, int newSerialNumber, boolean expectedResult) {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getPrefix()).thenReturn("OLD");
        when(mockAutoNumber.getPostfix()).thenReturn("OLD");
        when(mockAutoNumber.getStartNumber()).thenReturn(1);
        when(mockAutoNumber.getSerialNumber()).thenReturn(4);
        
        // 创建新的自增编号
        AutoNumber newAutoNumber = mock(AutoNumber.class);
        when(newAutoNumber.getPrefix()).thenReturn(newPrefix);
        when(newAutoNumber.getPostfix()).thenReturn(newPostfix);
        when(newAutoNumber.getStartNumber()).thenReturn(newStartNumber);
        when(newAutoNumber.getSerialNumber()).thenReturn(newSerialNumber);
        
        // Act: 执行isEffectiveImmediately方法
        boolean result = autoNumberExt.isEffectiveImmediately(newAutoNumber);
        
        // Assert: 验证结果
        assertEquals(expectedResult, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEffectiveImmediately方法在传入null时返回false
     */
    @Test
    @DisplayName("边界条件 - isEffectiveImmediately方法传入null")
    void testIsEffectiveImmediately_NullParameter() {
        // Act: 执行isEffectiveImmediately方法
        boolean result = autoNumberExt.isEffectiveImmediately(null);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAutoNumberTypeWithDefault方法能够正确获取默认类型
     */
    @Test
    @DisplayName("默认类型 - getAutoNumberTypeWithDefault方法返回默认类型")
    void testGetAutoNumberTypeWithDefault_DefaultType() {
        // Arrange: 设置自增编号类型为null
        when(mockAutoNumber.getAutoNumberType()).thenReturn(null);
        
        // Act: 执行getAutoNumberTypeWithDefault方法
        String result = autoNumberExt.getAutoNumberTypeWithDefault();
        
        // Assert: 验证结果
        assertEquals(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAutoNumberTypeWithDefault方法能够正确获取自定义类型
     */
    @Test
    @DisplayName("默认类型 - getAutoNumberTypeWithDefault方法返回自定义类型")
    void testGetAutoNumberTypeWithDefault_CustomType() {
        // Arrange: 设置自增编号类型
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION);
        
        // Act: 执行getAutoNumberTypeWithDefault方法
        String result = autoNumberExt.getAutoNumberTypeWithDefault();
        
        // Assert: 验证结果
        assertEquals(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isFunctionAutoNumber方法能够正确判断是否为函数自增编号
     */
    @ParameterizedTest
    @ValueSource(strings = {
        AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION,
        AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL,
        "other_type",
        ""
    })
    @DisplayName("函数类型 - isFunctionAutoNumber方法")
    void testIsFunctionAutoNumber(String autoNumberType) {
        // Arrange: 设置自增编号类型
        when(mockAutoNumber.getAutoNumberType()).thenReturn(autoNumberType);
        
        // Act: 执行isFunctionAutoNumber方法
        boolean result = autoNumberExt.isFunctionAutoNumber();
        
        // Assert: 验证结果
        assertEquals(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION.equals(autoNumberType), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法对于普通自增编号的比较
     */
    @Test
    @DisplayName("对象比较 - equals方法普通自增编号")
    void testEquals_NormalAutoNumber() {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getCondition()).thenReturn("NONE");
        when(mockAutoNumber.getPrefix()).thenReturn("TEST");
        when(mockAutoNumber.getPostfix()).thenReturn("END");
        when(mockAutoNumber.getSerialNumber()).thenReturn(4);
        when(mockAutoNumber.getStartNumber()).thenReturn(1);
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);
        
        // 创建相同的自增编号
        AutoNumber sameAutoNumber = mock(AutoNumber.class);
        when(sameAutoNumber.getApiName()).thenReturn("test_number");
        when(sameAutoNumber.getCondition()).thenReturn("NONE");
        when(sameAutoNumber.getPrefix()).thenReturn("TEST");
        when(sameAutoNumber.getPostfix()).thenReturn("END");
        when(sameAutoNumber.getSerialNumber()).thenReturn(4);
        when(sameAutoNumber.getStartNumber()).thenReturn(1);
        when(sameAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);
        
        AutoNumberExt anotherExt = AutoNumberExt.of(sameAutoNumber);
        
        // Act & Assert: 验证equals方法
        assertEquals(autoNumberExt, anotherExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法对于函数自增编号的比较
     */
    @Test
    @DisplayName("对象比较 - equals方法函数自增编号")
    void testEquals_FunctionAutoNumber() {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION);
        when(mockAutoNumber.getFuncApiName()).thenReturn("func1");
        
        // 创建相同的自增编号
        AutoNumber sameAutoNumber = mock(AutoNumber.class);
        when(sameAutoNumber.getApiName()).thenReturn("test_number");
        when(sameAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION);
        when(sameAutoNumber.getFuncApiName()).thenReturn("func1");
        
        AutoNumberExt anotherExt = AutoNumberExt.of(sameAutoNumber);
        
        // Act & Assert: 验证equals方法
        assertEquals(autoNumberExt, anotherExt);
        
        // 创建不同的自增编号
        AutoNumber differentAutoNumber = mock(AutoNumber.class);
        when(differentAutoNumber.getApiName()).thenReturn("test_number");
        when(differentAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION);
        when(differentAutoNumber.getFuncApiName()).thenReturn("func2");
        
        AutoNumberExt differentExt = AutoNumberExt.of(differentAutoNumber);
        
        // Act & Assert: 验证equals方法
        assertNotEquals(autoNumberExt, differentExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hashCode方法能够正确生成哈希码
     */
    @Test
    @DisplayName("对象比较 - hashCode方法")
    void testHashCode() {
        // Arrange: 设置原始自增编号属性
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getCondition()).thenReturn("NONE");
        when(mockAutoNumber.getPrefix()).thenReturn("TEST");
        when(mockAutoNumber.getPostfix()).thenReturn("END");
        when(mockAutoNumber.getSerialNumber()).thenReturn(4);
        when(mockAutoNumber.getStartNumber()).thenReturn(1);
        when(mockAutoNumber.getAutoNumberType()).thenReturn(AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);
        
        // Act: 执行hashCode方法
        int result = autoNumberExt.hashCode();
        
        // Assert: 验证结果
        assertNotEquals(0, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值的正确性
     */
    @Test
    @DisplayName("常量验证 - 常量值正确性")
    void testConstants() {
        // Assert: 验证常量值
        assertEquals("NONE", AutoNumberExt.NONE);
        assertEquals("normal", AutoNumberExt.AUTO_NUMBER_TYPE_NORMAL);
        assertEquals("function", AutoNumberExt.AUTO_NUMBER_TYPE_FUNCTION);
        
        // 验证条件列表
        List<String> conditionList = AutoNumberExt.conditionList;
        assertTrue(conditionList.contains("NONE"));
        assertTrue(conditionList.contains("DAY"));
        assertTrue(conditionList.contains("MONTH"));
        assertTrue(conditionList.contains("YEAR"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockAutoNumber.getApiName()).thenReturn("test_number");
        when(mockAutoNumber.getLabel()).thenReturn("测试编号");
        when(mockAutoNumber.getCondition()).thenReturn("NONE");
        when(mockAutoNumber.getPrefix()).thenReturn("TEST");
        when(mockAutoNumber.getPostfix()).thenReturn("END");
        
        // Act & Assert: 验证委托的方法
        assertEquals("test_number", autoNumberExt.getApiName());
        assertEquals("测试编号", autoNumberExt.getLabel());
        assertEquals("NONE", autoNumberExt.getCondition());
        assertEquals("TEST", autoNumberExt.getPrefix());
        assertEquals("END", autoNumberExt.getPostfix());
        
        // 验证委托调用
        verify(mockAutoNumber).getApiName();
        verify(mockAutoNumber).getLabel();
        verify(mockAutoNumber).getCondition();
        verify(mockAutoNumber).getPrefix();
        verify(mockAutoNumber).getPostfix();
    }
}
