package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FormComponentExt的JUnit 5测试类
 * 测试表单组件扩展功能
 * 
 * GenerateByAI
 * 测试内容描述：测试表单组件的字段管理、分组操作和业务逻辑
 */
class FormComponentExtJUnit5Test {

    private FormComponentExt formComponentExt;
    private IFormComponent testFormComponent;
    private ObjectDescribeExt testDescribeExt;

    @BeforeEach
    void setUp() {
        // 创建测试用的表单组件
        testFormComponent = new FormComponent();
        testFormComponent.setName("test_form");
        
        // 创建测试字段分组
        List<IFieldSection> fieldSections = Lists.newArrayList();
        
        // 基础字段分组
        FieldSection baseSection = new FieldSection();
        baseSection.setName(FormComponentExt.BASE_FIELD_SECTION_API_NAME);
        baseSection.setHeader("基础信息");
        
        List<IFormField> baseFields = Lists.newArrayList();
        
        FormField field1 = new FormField();
        field1.setFieldName("field1");
        field1.setRenderType("text");
        field1.setRequired(false);
        field1.setReadOnly(false);
        baseFields.add(field1);
        
        FormField field2 = new FormField();
        field2.setFieldName("field2");
        field2.setRenderType("number");
        field2.setRequired(true);
        field2.setReadOnly(false);
        baseFields.add(field2);
        
        baseSection.setFields(baseFields);
        fieldSections.add(baseSection);
        
        // 其他字段分组
        FieldSection otherSection = new FieldSection();
        otherSection.setName("other_section");
        otherSection.setHeader("其他信息");
        
        List<IFormField> otherFields = Lists.newArrayList();
        
        FormField field3 = new FormField();
        field3.setFieldName("field3");
        field3.setRenderType("date");
        field3.setRequired(false);
        field3.setReadOnly(true);
        otherFields.add(field3);
        
        otherSection.setFields(otherFields);
        fieldSections.add(otherSection);
        
        testFormComponent.setFieldSections(fieldSections);
        formComponentExt = FormComponentExt.of(testFormComponent);
        
        // 创建测试对象描述
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("TestObject");
        
        TextFieldDescribe fieldDescribe1 = new TextFieldDescribe();
        fieldDescribe1.setApiName("field1");
        fieldDescribe1.setLabel("字段1");
        fieldDescribe1.setRequired(false);
        
        TextFieldDescribe fieldDescribe2 = new TextFieldDescribe();
        fieldDescribe2.setApiName("field2");
        fieldDescribe2.setLabel("字段2");
        fieldDescribe2.setRequired(true);
        
        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe1, fieldDescribe2));
        testDescribeExt = ObjectDescribeExt.of(objectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法
     */
    @Test
    @DisplayName("静态方法 - of工厂方法")
    void testOf_WithIFormComponent() {
        // Act: 使用of方法创建实例
        FormComponentExt result = FormComponentExt.of(testFormComponent);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(testFormComponent, result.getFormComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 避免双重包装
     */
    @Test
    @DisplayName("静态方法 - of工厂方法避免双重包装")
    void testOf_AvoidDoubleWrapping() {
        // Arrange: 创建FormComponentExt实例
        FormComponentExt originalExt = FormComponentExt.of(testFormComponent);
        
        // Act: 再次使用of方法包装
        FormComponentExt result = FormComponentExt.of(originalExt);
        
        // Assert: 验证避免双重包装
        assertNotNull(result);
        assertSame(testFormComponent, result.getFormComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateFormField静态方法
     */
    @Test
    @DisplayName("静态方法 - generateFormField方法")
    void testGenerateFormField() {
        // Act: 执行generateFormField静态方法
        FormField result = FormComponentExt.generateFormField("test_field", "text", true, false);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("test_field", result.getFieldName());
        assertTrue(result.isReadOnly());
        assertFalse(result.isRequired());
        assertNotNull(result.getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hasNoField方法 - 有字段
     */
    @Test
    @DisplayName("字段检查 - hasNoField方法有字段")
    void testHasNoField_WithFields() {
        // Act: 执行hasNoField方法
        boolean result = formComponentExt.hasNoField();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hasNoField方法 - 无字段
     */
    @Test
    @DisplayName("字段检查 - hasNoField方法无字段")
    void testHasNoField_WithoutFields() {
        // Arrange: 创建无字段的表单组件
        IFormComponent emptyFormComponent = new FormComponent();
        emptyFormComponent.setFieldSections(Lists.newArrayList());
        FormComponentExt emptyExt = FormComponentExt.of(emptyFormComponent);
        
        // Act: 执行hasNoField方法
        boolean result = emptyExt.hasNoField();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getBaseFieldSection方法 - 存在基础分组
     */
    @Test
    @DisplayName("分组获取 - getBaseFieldSection方法存在基础分组")
    void testGetBaseFieldSection_Exists() {
        // Act: 执行getBaseFieldSection方法
        Optional<IFieldSection> result = formComponentExt.getBaseFieldSection();
        
        // Assert: 验证结果
        assertTrue(result.isPresent());
        assertEquals(FormComponentExt.BASE_FIELD_SECTION_API_NAME, result.get().getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getBaseFieldSection方法 - 不存在基础分组
     */
    @Test
    @DisplayName("分组获取 - getBaseFieldSection方法不存在基础分组")
    void testGetBaseFieldSection_NotExists() {
        // Arrange: 创建无基础分组的表单组件
        IFormComponent noBaseFormComponent = new FormComponent();
        FieldSection otherSection = new FieldSection();
        otherSection.setName("other_section");
        noBaseFormComponent.setFieldSections(Lists.newArrayList(otherSection));
        FormComponentExt noBaseExt = FormComponentExt.of(noBaseFormComponent);
        
        // Act: 执行getBaseFieldSection方法
        Optional<IFieldSection> result = noBaseExt.getBaseFieldSection();
        
        // Assert: 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getField方法 - 字段存在
     */
    @Test
    @DisplayName("字段获取 - getField方法字段存在")
    void testGetField_Exists() {
        // Act: 执行getField方法
        Optional<IFormField> result = formComponentExt.getField("field1");
        
        // Assert: 验证结果
        assertTrue(result.isPresent());
        assertEquals("field1", result.get().getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getField方法 - 字段不存在
     */
    @Test
    @DisplayName("字段获取 - getField方法字段不存在")
    void testGetField_NotExists() {
        // Act: 执行getField方法
        Optional<IFormField> result = formComponentExt.getField("non_existent_field");
        
        // Assert: 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hideFields方法
     */
    @Test
    @DisplayName("字段隐藏 - hideFields方法")
    void testHideFields() {
        // Arrange: 准备要隐藏的字段集合
        Set<String> toHide = Sets.newHashSet("field1");
        
        // Act: 执行hideFields方法
        formComponentExt.hideFields(toHide);
        
        // Assert: 验证结果
        Optional<IFormField> hiddenField = formComponentExt.getField("field1");
        assertFalse(hiddenField.isPresent());
        
        Optional<IFormField> remainingField = formComponentExt.getField("field2");
        assertTrue(remainingField.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setRequired方法
     */
    @Test
    @DisplayName("字段设置 - setRequired方法")
    void testSetRequired() {
        // Act: 执行setRequired方法
        formComponentExt.setRequired(testDescribeExt);
        
        // Assert: 验证结果
        Optional<IFormField> field1 = formComponentExt.getField("field1");
        assertTrue(field1.isPresent());
        assertFalse(field1.get().isRequired());
        
        Optional<IFormField> field2 = formComponentExt.getField("field2");
        assertTrue(field2.isPresent());
        assertTrue(field2.get().isRequired());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeEmptySection方法
     */
    @Test
    @DisplayName("分组清理 - removeEmptySection方法")
    void testRemoveEmptySection() {
        // Arrange: 添加空分组
        FieldSection emptySection = new FieldSection();
        emptySection.setName("empty_section");
        emptySection.setFields(Lists.newArrayList());
        testFormComponent.getFieldSections().add(emptySection);
        
        // Act: 执行removeEmptySection方法
        formComponentExt.removeEmptySection();
        
        // Assert: 验证结果
        List<IFieldSection> sections = formComponentExt.getFieldSections();
        assertEquals(2, sections.size()); // 只保留有字段的分组
        assertFalse(sections.stream().anyMatch(s -> "empty_section".equals(s.getName())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试adjustFieldRenderType方法
     */
    @Test
    @DisplayName("字段调整 - adjustFieldRenderType方法")
    void testAdjustFieldRenderType() {
        // Act: 执行adjustFieldRenderType方法
        assertDoesNotThrow(() -> formComponentExt.adjustFieldRenderType("TestObject"));
        
        // Assert: 验证方法执行不抛异常
        assertNotNull(formComponentExt.getField("field1"));
        assertNotNull(formComponentExt.getField("field2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addFields方法 - 基础分组存在
     */
    @Test
    @DisplayName("字段添加 - addFields方法基础分组存在")
    void testAddFields_BaseExists() {
        // Arrange: 准备要添加的字段
        List<IFormField> newFields = Lists.newArrayList();
        FormField newField = new FormField();
        newField.setFieldName("new_field");
        newFields.add(newField);
        
        // Act: 执行addFields方法
        formComponentExt.addFields(newFields);
        
        // Assert: 验证结果
        Optional<IFormField> addedField = formComponentExt.getField("new_field");
        assertTrue(addedField.isPresent());
        assertEquals("new_field", addedField.get().getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addFields方法 - 空字段列表
     */
    @Test
    @DisplayName("字段添加 - addFields方法空字段列表")
    void testAddFields_EmptyList() {
        // Arrange: 记录原始字段数量
        int originalFieldCount = (int) formComponentExt.stream().count();
        
        // Act: 执行addFields方法
        formComponentExt.addFields(Lists.newArrayList());
        
        // Assert: 验证结果 - 字段数量不变
        int currentFieldCount = (int) formComponentExt.stream().count();
        assertEquals(originalFieldCount, currentFieldCount);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试stream方法
     */
    @Test
    @DisplayName("流操作 - stream方法")
    void testStream() {
        // Act: 执行stream方法
        List<IFormField> fields = formComponentExt.stream().collect(Collectors.toList());
        
        // Assert: 验证结果
        assertEquals(3, fields.size());
        assertTrue(fields.stream().anyMatch(f -> "field1".equals(f.getFieldName())));
        assertTrue(fields.stream().anyMatch(f -> "field2".equals(f.getFieldName())));
        assertTrue(fields.stream().anyMatch(f -> "field3".equals(f.getFieldName())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getReferenceFieldConfig方法
     */
    @Test
    @DisplayName("配置获取 - getReferenceFieldConfig方法")
    void testGetReferenceFieldConfig() {
        // Arrange: 设置引用字段配置
        formComponentExt.setReferenceFieldConfig("test_layout");
        
        // Act: 执行getReferenceFieldConfig方法
        String result = formComponentExt.getReferenceFieldConfig();
        
        // Assert: 验证结果
        assertEquals("test_layout", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isReferenceLayoutFieldConfig方法 - 有配置
     */
    @Test
    @DisplayName("配置检查 - isReferenceLayoutFieldConfig方法有配置")
    void testIsReferenceLayoutFieldConfig_WithConfig() {
        // Arrange: 设置引用字段配置
        formComponentExt.setReferenceFieldConfig("test_layout");
        
        // Act: 执行isReferenceLayoutFieldConfig方法
        boolean result = formComponentExt.isReferenceLayoutFieldConfig();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isReferenceLayoutFieldConfig方法 - 无配置
     */
    @Test
    @DisplayName("配置检查 - isReferenceLayoutFieldConfig方法无配置")
    void testIsReferenceLayoutFieldConfig_WithoutConfig() {
        // Act: 执行isReferenceLayoutFieldConfig方法
        boolean result = formComponentExt.isReferenceLayoutFieldConfig();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托模式的方法调用
     */
    @Test
    @DisplayName("委托模式 - 委托方法调用")
    void testDelegateMethodCalls() {
        // Act & Assert: 验证委托方法调用
        assertEquals("test_form", formComponentExt.getName());
        assertNotNull(formComponentExt.getFieldSections());
        assertEquals(2, formComponentExt.getFieldSections().size());
        
        // 测试设置方法
        formComponentExt.setName("new_form_name");
        assertEquals("new_form_name", formComponentExt.getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多种渲染类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"text", "number", "date", "lookup", "textarea"})
    @DisplayName("字段生成 - 多种渲染类型测试")
    void testGenerateFormField_VariousRenderTypes(String renderType) {
        // Act: 执行generateFormField方法
        FormField result = FormComponentExt.generateFormField("test_field", renderType, false, true);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("test_field", result.getFieldName());
        assertFalse(result.isReadOnly());
        assertTrue(result.isRequired());
        assertNotNull(result.getRenderType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - 空字段分组
     */
    @Test
    @DisplayName("边界条件 - 空字段分组")
    void testNullFieldSections() {
        // Arrange: 创建空字段分组的表单组件
        IFormComponent nullFormComponent = new FormComponent();
        nullFormComponent.setFieldSections(Lists.newArrayList());
        FormComponentExt nullExt = FormComponentExt.of(nullFormComponent);

        // Act & Assert: 验证空字段分组处理
        assertTrue(nullExt.hasNoField());
        assertFalse(nullExt.getBaseFieldSection().isPresent());
        assertFalse(nullExt.getField("any_field").isPresent());

        // 测试操作不抛异常
        assertDoesNotThrow(() -> nullExt.hideFields(Sets.newHashSet("any_field")));
        assertDoesNotThrow(() -> nullExt.removeEmptySection());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的数据
        String name1 = formComponentExt.getName();
        String name2 = formComponentExt.getName();
        List<IFieldSection> sections1 = formComponentExt.getFieldSections();
        List<IFieldSection> sections2 = formComponentExt.getFieldSections();
        
        // Assert: 验证数据一致性
        assertEquals(name1, name2);
        assertEquals(sections1.size(), sections2.size());
        assertSame(formComponentExt.getFormComponent(), formComponentExt.getFormComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全 - 多线程访问")
    void testThreadSafety() throws InterruptedException {
        // Act: 在多线程中访问
        Thread thread1 = new Thread(() -> {
            formComponentExt.hideFields(Sets.newHashSet("field1"));
            assertNotNull(formComponentExt.getName());
        });
        
        Thread thread2 = new Thread(() -> {
            formComponentExt.setRequired(testDescribeExt);
            assertNotNull(formComponentExt.getFieldSections());
        });
        
        // Assert: 验证线程安全
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }
}
