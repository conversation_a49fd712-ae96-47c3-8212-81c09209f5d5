package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.impl.UIEvent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * UIEventExt的JUnit 5测试类
 * 测试UI事件扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试UI事件扩展的创建、事件类型判断和触发器处理功能
 */
class UIEventExtJUnit5Test {

    private IUIEvent mockUIEvent;
    private UIEventExt uiEventExt;
    private Map<String, Object> eventMap;

    @BeforeEach
    void setUp() {
        mockUIEvent = mock(IUIEvent.class);
        uiEventExt = UIEventExt.of(mockUIEvent);
        
        // 创建测试用的事件Map
        eventMap = new HashMap<>();
        eventMap.put("type", 1); // FIELD类型
        eventMap.put("triggers", Lists.newArrayList(1, 2)); // BLUR, ADD_LINE
        eventMap.put("trigger_field_api_names", Lists.newArrayList("name", "email"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - IUIEvent参数
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例(IUIEvent)")
    void testOf_UIEvent() {
        // Act: 使用of方法创建实例
        UIEventExt result = UIEventExt.of(mockUIEvent);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockUIEvent, result.getUiEvent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - Map参数
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例(Map)")
    void testOf_Map() {
        // Act: 使用of方法创建实例
        UIEventExt result = UIEventExt.of(eventMap);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertNotNull(result.getUiEvent());
        assertEquals(1, result.getType());
        assertEquals(Lists.newArrayList(1, 2), result.getTriggers());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - Map参数 - null触发字段
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例(Map) - null触发字段")
    void testOf_Map_NullTriggerFields() {
        // Arrange: 创建没有触发字段的Map
        Map<String, Object> mapWithoutTriggerFields = new HashMap<>(eventMap);
        mapWithoutTriggerFields.remove("trigger_field_api_names");
        
        // Act: 使用of方法创建实例
        UIEventExt result = UIEventExt.of(mapWithoutTriggerFields);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertNotNull(result.getTriggerFieldApiNames());
        assertTrue(result.getTriggerFieldApiNames().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ofList工厂方法
     */
    @Test
    @DisplayName("工厂方法 - ofList方法创建实例列表")
    void testOfList() {
        // Arrange: 创建测试用的事件Map列表
        List<Map<String, Object>> eventMapList = new ArrayList<>();
        eventMapList.add(eventMap);
        
        Map<String, Object> anotherEventMap = new HashMap<>();
        anotherEventMap.put("type", 2); // DETAIL_OBJECT类型
        anotherEventMap.put("triggers", Lists.newArrayList(3, 4)); // EDIT_LINE, DELETE_LINE
        eventMapList.add(anotherEventMap);
        
        // Act: 使用ofList方法创建实例列表
        List<UIEventExt> result = UIEventExt.ofList(eventMapList);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getType());
        assertEquals(2, result.get(1).getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEventEnumType方法
     */
    @ParameterizedTest
    @ValueSource(ints = {0, 1, 2, 3, 4, 5, 6})
    @DisplayName("事件类型 - getEventEnumType方法")
    void testGetEventEnumType(int type) {
        // Arrange: 设置事件类型
        when(mockUIEvent.getType()).thenReturn(type);
        
        // Act: 执行getEventEnumType方法
        UIEventExt.EventType result = uiEventExt.getEventEnumType();
        
        // Assert: 验证结果
        if (type == 3) { // VERIFY类型
            assertEquals(UIEventExt.EventType.CHECK, result);
        } else {
            assertEquals(UIEventExt.EventType.UPDATE, result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toUIEvent方法
     */
    @Test
    @DisplayName("转换方法 - toUIEvent方法")
    void testToUIEvent() {
        // Act: 执行toUIEvent方法
        IUIEvent result = uiEventExt.toUIEvent();
        
        // Assert: 验证结果
        assertSame(mockUIEvent, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setTriggerFieldApiNamesNullToEmptyList方法 - 空列表
     */
    @Test
    @DisplayName("触发字段 - setTriggerFieldApiNamesNullToEmptyList方法 - 空列表")
    void testSetTriggerFieldApiNamesNullToEmptyList_EmptyList() {
        // Arrange: 设置触发字段为空列表
        when(mockUIEvent.getTriggerFieldApiNames()).thenReturn(Collections.emptyList());

        // Act: 执行setTriggerFieldApiNamesNullToEmptyList方法
        uiEventExt.setTriggerFieldApiNamesNullToEmptyList();

        // Assert: 验证setTriggerFieldApiNames方法被调用
        // 注意：根据UIEventExt的实现，即使是空列表也会调用setTriggerFieldApiNames
        verify(mockUIEvent).setTriggerFieldApiNames(Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setTriggerFieldApiNamesNullToEmptyList方法 - null列表
     */
    @Test
    @DisplayName("触发字段 - setTriggerFieldApiNamesNullToEmptyList方法 - null列表")
    void testSetTriggerFieldApiNamesNullToEmptyList_NullList() {
        // Arrange: 设置触发字段为null
        when(mockUIEvent.getTriggerFieldApiNames()).thenReturn(null);
        
        // Act: 执行setTriggerFieldApiNamesNullToEmptyList方法
        uiEventExt.setTriggerFieldApiNamesNullToEmptyList();
        
        // Assert: 验证setTriggerFieldApiNames方法被调用
        verify(mockUIEvent).setTriggerFieldApiNames(Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试remove方法
     */
    @Test
    @DisplayName("文档操作 - remove方法")
    void testRemove() {
        // 由于UIEventExt.remove方法需要将IUIEvent转换为DocumentBasedBean
        // 这个测试需要使用真实的UIEvent对象而不是Mock

        // Arrange: 创建一个真实的UIEvent对象
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("test_key", "test_value");
        UIEvent realUIEvent = new UIEvent(eventData);
        UIEventExt realUIEventExt = UIEventExt.of(realUIEvent);

        // Act: 执行remove方法
        realUIEventExt.remove("test_key");

        // Assert: 验证键已被移除
        assertNull(realUIEvent.get("test_key"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isFieldInTriggerList方法 - 字段在列表中
     */
    @Test
    @DisplayName("触发字段 - isFieldInTriggerList方法 - 字段在列表中")
    void testIsFieldInTriggerList_FieldInList() {
        // Arrange: 设置触发字段列表
        List<String> triggerFields = Lists.newArrayList("name", "email", "phone");
        when(mockUIEvent.getTriggerFieldApiNames()).thenReturn(triggerFields);
        
        // Act & Assert: 验证字段在列表中
        assertTrue(uiEventExt.isFieldInTriggerList("name"));
        assertTrue(uiEventExt.isFieldInTriggerList("email"));
        assertTrue(uiEventExt.isFieldInTriggerList("phone"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isFieldInTriggerList方法 - 字段不在列表中
     */
    @Test
    @DisplayName("触发字段 - isFieldInTriggerList方法 - 字段不在列表中")
    void testIsFieldInTriggerList_FieldNotInList() {
        // Arrange: 设置触发字段列表
        List<String> triggerFields = Lists.newArrayList("name", "email", "phone");
        when(mockUIEvent.getTriggerFieldApiNames()).thenReturn(triggerFields);
        
        // Act & Assert: 验证字段不在列表中
        assertFalse(uiEventExt.isFieldInTriggerList("address"));
        assertFalse(uiEventExt.isFieldInTriggerList("company"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEventType方法 - 有类型
     */
    @Test
    @DisplayName("事件类型 - getEventType方法 - 有类型")
    void testGetEventType_WithType() {
        // Arrange: 设置事件类型
        when(mockUIEvent.getType()).thenReturn(2); // DETAIL_OBJECT类型
        
        // Act: 执行getEventType方法
        int result = uiEventExt.getEventType();
        
        // Assert: 验证结果
        assertEquals(2, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEventType方法 - 无类型
     */
    @Test
    @DisplayName("事件类型 - getEventType方法 - 无类型")
    void testGetEventType_WithoutType() {
        // Arrange: 设置事件类型为null
        when(mockUIEvent.getType()).thenReturn(null);
        
        // Act: 执行getEventType方法
        int result = uiEventExt.getEventType();
        
        // Assert: 验证结果为UNDEFINED
        assertEquals(UIEventType.UNDEFINED.getStatus(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDetailObjectEvent方法 - 是从对象事件
     */
    @Test
    @DisplayName("事件类型 - isDetailObjectEvent方法 - 是从对象事件")
    void testIsDetailObjectEvent_True() {
        // Arrange: 设置事件类型为DETAIL_OBJECT
        when(mockUIEvent.getType()).thenReturn(UIEventType.DETAIL_OBJECT.getStatus());
        
        // Act: 执行isDetailObjectEvent方法
        boolean result = uiEventExt.isDetailObjectEvent();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDetailObjectEvent方法 - 不是从对象事件
     */
    @Test
    @DisplayName("事件类型 - isDetailObjectEvent方法 - 不是从对象事件")
    void testIsDetailObjectEvent_False() {
        // Arrange: 设置事件类型为FIELD
        when(mockUIEvent.getType()).thenReturn(UIEventType.FIELD.getStatus());
        
        // Act: 执行isDetailObjectEvent方法
        boolean result = uiEventExt.isDetailObjectEvent();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containTrigger方法 - 包含触发器
     */
    @Test
    @DisplayName("触发器 - containTrigger方法 - 包含触发器")
    void testContainTrigger_ContainsTrigger() {
        // Arrange: 设置触发器列表
        List<Integer> triggers = Lists.newArrayList(1, 2, 3);
        when(mockUIEvent.getTriggers()).thenReturn(triggers);
        
        // 创建目标触发器列表
        List<UIEventTrigger> targetTriggers = Lists.newArrayList(
            UIEventTrigger.BLUR,           // code = 1
            UIEventTrigger.EDIT_LINE       // code = 3
        );
        
        // Act: 执行containTrigger方法
        boolean result = uiEventExt.containTrigger(triggers, targetTriggers);
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containTrigger方法 - 不包含触发器
     */
    @Test
    @DisplayName("触发器 - containTrigger方法 - 不包含触发器")
    void testContainTrigger_DoesNotContainTrigger() {
        // Arrange: 设置触发器列表
        List<Integer> triggers = Lists.newArrayList(1, 2);
        when(mockUIEvent.getTriggers()).thenReturn(triggers);
        
        // 创建目标触发器列表
        List<UIEventTrigger> targetTriggers = Lists.newArrayList(
            UIEventTrigger.PAGE_ONLOAD,    // code = 5
            UIEventTrigger.DELETE_LINE     // code = 4
        );
        
        // Act: 执行containTrigger方法
        boolean result = uiEventExt.containTrigger(triggers, targetTriggers);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containTrigger方法 - 空触发器列表
     */
    @Test
    @DisplayName("触发器 - containTrigger方法 - 空触发器列表")
    void testContainTrigger_EmptyTriggers() {
        // Arrange: 设置空触发器列表
        when(mockUIEvent.getTriggers()).thenReturn(Collections.emptyList());
        
        // 创建目标触发器列表
        List<UIEventTrigger> targetTriggers = Lists.newArrayList(
            UIEventTrigger.BLUR,           // code = 1
            UIEventTrigger.ADD_LINE        // code = 2
        );
        
        // Act: 执行containTrigger方法
        boolean result = uiEventExt.containTrigger(Lists.newArrayList(1, 2), targetTriggers);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containTrigger方法 - 空目标触发器列表
     */
    @Test
    @DisplayName("触发器 - containTrigger方法 - 空目标触发器列表")
    void testContainTrigger_EmptyTargetTriggers() {
        // Arrange: 设置触发器列表
        List<Integer> triggers = Lists.newArrayList(1, 2, 3);
        when(mockUIEvent.getTriggers()).thenReturn(triggers);
        
        // Act: 执行containTrigger方法
        boolean result = uiEventExt.containTrigger(triggers, Collections.emptyList());
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockUIEvent.getType()).thenReturn(1);
        when(mockUIEvent.getTriggers()).thenReturn(Lists.newArrayList(1, 2));
        when(mockUIEvent.getTriggerFieldApiNames()).thenReturn(Lists.newArrayList("name", "email"));
        
        // Act & Assert: 验证委托的方法
        assertEquals(1, uiEventExt.getType());
        assertEquals(Lists.newArrayList(1, 2), uiEventExt.getTriggers());
        assertEquals(Lists.newArrayList("name", "email"), uiEventExt.getTriggerFieldApiNames());
        
        // 验证委托调用
        verify(mockUIEvent).getType();
        verify(mockUIEvent).getTriggers();
        verify(mockUIEvent).getTriggerFieldApiNames();
    }
}
