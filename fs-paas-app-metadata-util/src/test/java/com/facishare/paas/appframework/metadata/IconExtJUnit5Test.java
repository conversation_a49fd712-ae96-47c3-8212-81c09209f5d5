package com.facishare.paas.appframework.metadata;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IconExt的JUnit 5测试类
 * 测试图标扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试图标扩展的创建、构建器模式和数据操作功能
 */
class IconExtJUnit5Test {

    private IconExt iconExt;

    @BeforeEach
    void setUp() {
        iconExt = new IconExt();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试默认构造函数
     */
    @Test
    @DisplayName("构造函数 - 默认构造函数")
    void testDefaultConstructor() {
        // Act: 创建默认实例
        IconExt result = new IconExt();
        
        // Assert: 验证默认构造函数
        assertNotNull(result);
        assertNull(result.getIconIndex());
        assertNull(result.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试全参构造函数
     */
    @Test
    @DisplayName("构造函数 - 全参构造函数")
    void testAllArgsConstructor() {
        // Act: 使用全参构造函数创建实例
        IconExt result = new IconExt(1, "/path/to/icon.png");

        // Assert: 验证全参构造函数
        assertNotNull(result);
        assertEquals(1, result.getIconIndex());
        assertEquals("/path/to/icon.png", result.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式
     */
    @Test
    @DisplayName("构建器模式 - Builder模式创建实例")
    void testBuilder() {
        // Act: 使用Builder模式创建实例
        IconExt result = IconExt.builder()
                .iconIndex(2)
                .iconPath("/path/to/icon2.png")
                .build();

        // Assert: 验证Builder模式
        assertNotNull(result);
        assertEquals(2, result.getIconIndex());
        assertEquals("/path/to/icon2.png", result.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式 - 部分属性
     */
    @Test
    @DisplayName("构建器模式 - Builder模式部分属性")
    void testBuilder_PartialProperties() {
        // Act: 使用Builder模式创建只设置部分属性的实例
        IconExt result1 = IconExt.builder()
                .iconIndex(3)
                .build();
        
        IconExt result2 = IconExt.builder()
                .iconPath("/path/to/icon3.png")
                .build();

        // Assert: 验证Builder模式部分属性
        assertNotNull(result1);
        assertEquals(3, result1.getIconIndex());
        assertNull(result1.getIconPath());

        assertNotNull(result2);
        assertNull(result2.getIconIndex());
        assertEquals("/path/to/icon3.png", result2.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试iconIndex的getter和setter
     */
    @Test
    @DisplayName("属性访问 - iconIndex的getter和setter")
    void testIconIndexGetterSetter() {
        // Act: 设置iconIndex
        iconExt.setIconIndex(5);
        
        // Assert: 验证iconIndex的getter和setter
        assertEquals(5, iconExt.getIconIndex());
        
        // Act: 设置为null
        iconExt.setIconIndex(null);
        
        // Assert: 验证null值
        assertNull(iconExt.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试iconPath的getter和setter
     */
    @Test
    @DisplayName("属性访问 - iconPath的getter和setter")
    void testIconPathGetterSetter() {
        // Act: 设置iconPath
        iconExt.setIconPath("/path/to/icon4.png");

        // Assert: 验证iconPath的getter和setter
        assertEquals("/path/to/icon4.png", iconExt.getIconPath());

        // Act: 设置为null
        iconExt.setIconPath(null);

        // Assert: 验证null值
        assertNull(iconExt.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化iconIndex值
     */
    @ParameterizedTest
    @ValueSource(ints = {0, 1, 10, 100, 999, -1})
    @DisplayName("参数化测试 - iconIndex多种值")
    void testIconIndex_ParameterizedValues(int iconIndex) {
        // Act: 设置iconIndex
        iconExt.setIconIndex(iconIndex);
        
        // Assert: 验证iconIndex值
        assertEquals(iconIndex, iconExt.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化iconPath值
     */
    @ParameterizedTest
    @ValueSource(strings = {"/path/to/icon1.png", "/path/to/icon2.jpg", "/icons/home.svg", "/assets/user.gif", "icon.ico", "relative/path.png"})
    @DisplayName("参数化测试 - iconPath多种路径值")
    void testIconPath_ParameterizedPaths(String iconPath) {
        // Act: 设置iconPath
        iconExt.setIconPath(iconPath);

        // Assert: 验证iconPath值
        assertEquals(iconPath, iconExt.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法
     */
    @Test
    @DisplayName("对象比较 - equals方法")
    void testEquals() {
        // Arrange: 创建相同的对象
        IconExt icon1 = new IconExt(1, "/path/to/icon.png");
        IconExt icon2 = new IconExt(1, "/path/to/icon.png");
        IconExt icon3 = new IconExt(2, "/path/to/icon.png");
        IconExt icon4 = new IconExt(1, "/path/to/other.png");
        
        // Assert: 验证equals方法
        assertEquals(icon1, icon2);
        assertNotEquals(icon1, icon3);
        assertNotEquals(icon1, icon4);
        assertEquals(icon1, icon1); // 自反性
        assertNotEquals(icon1, null); // null比较
        assertNotEquals(icon1, "string"); // 不同类型比较
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hashCode方法
     */
    @Test
    @DisplayName("对象比较 - hashCode方法")
    void testHashCode() {
        // Arrange: 创建相同的对象
        IconExt icon1 = new IconExt(1, "/path/to/icon.png");
        IconExt icon2 = new IconExt(1, "/path/to/icon.png");
        IconExt icon3 = new IconExt(2, "/path/to/icon.png");
        
        // Assert: 验证hashCode方法
        assertEquals(icon1.hashCode(), icon2.hashCode()); // 相等对象的hashCode相等
        assertNotEquals(icon1.hashCode(), icon3.hashCode()); // 不相等对象的hashCode通常不相等
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 创建对象
        IconExt icon = new IconExt(1, "/path/to/icon.png");

        // Act: 调用toString方法
        String result = icon.toString();

        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("IconExt"));
        assertTrue(result.contains("iconIndex=1"));
        assertTrue(result.contains("iconPath=/path/to/icon.png"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法 - null值
     */
    @Test
    @DisplayName("字符串表示 - toString方法null值")
    void testToString_NullValues() {
        // Arrange: 创建包含null值的对象
        IconExt icon = new IconExt(null, null);
        
        // Act: 调用toString方法
        String result = icon.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("IconExt"));
        assertTrue(result.contains("iconIndex=null"));
        assertTrue(result.contains("iconPath=null"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式的链式调用
     */
    @Test
    @DisplayName("构建器模式 - Builder链式调用")
    void testBuilder_ChainedCalls() {
        // Act: 使用链式调用创建实例
        IconExt result = IconExt.builder()
                .iconIndex(10)
                .iconPath("/path/to/icon.png")
                .iconIndex(20) // 覆盖之前的值
                .build();

        // Assert: 验证链式调用
        assertNotNull(result);
        assertEquals(20, result.getIconIndex()); // 最后设置的值
        assertEquals("/path/to/icon.png", result.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化构造函数和Builder的等价性
     */
    @ParameterizedTest
    @CsvSource({
        "1, '/path/to/icon1.png'",
        "2, '/path/to/icon2.jpg'",
        "3, '/icons/home.svg'",
        "0, '/assets/user.gif'",
        "-1, 'icon.ico'"
    })
    @DisplayName("参数化测试 - 构造函数和Builder等价性")
    void testConstructorBuilderEquivalence(int iconIndex, String iconPath) {
        // Act: 使用构造函数和Builder创建相同的对象
        IconExt constructorIcon = new IconExt(iconIndex, iconPath);
        IconExt builderIcon = IconExt.builder()
                .iconIndex(iconIndex)
                .iconPath(iconPath)
                .build();
        
        // Assert: 验证两种方式创建的对象相等
        assertEquals(constructorIcon, builderIcon);
        assertEquals(constructorIcon.hashCode(), builderIcon.hashCode());
        assertEquals(constructorIcon.toString(), builderIcon.toString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件
     */
    @Test
    @DisplayName("边界条件 - 边界值测试")
    void testBoundaryConditions() {
        // Act & Assert: 测试极值
        IconExt maxIcon = new IconExt(Integer.MAX_VALUE, "/path/to/max.png");
        assertEquals(Integer.MAX_VALUE, maxIcon.getIconIndex());
        assertEquals("/path/to/max.png", maxIcon.getIconPath());

        IconExt minIcon = new IconExt(Integer.MIN_VALUE, "");
        assertEquals(Integer.MIN_VALUE, minIcon.getIconIndex());
        assertEquals("", minIcon.getIconPath());

        // 测试空字符串
        IconExt emptyPathIcon = new IconExt(0, "");
        assertEquals("", emptyPathIcon.getIconPath());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Arrange: 设置属性
        iconExt.setIconIndex(100);
        iconExt.setIconPath("/path/to/test.png");

        // Act: 多次获取相同的数据
        Integer iconIndex1 = iconExt.getIconIndex();
        Integer iconIndex2 = iconExt.getIconIndex();
        String iconPath1 = iconExt.getIconPath();
        String iconPath2 = iconExt.getIconPath();

        // Assert: 验证数据一致性
        assertEquals(iconIndex1, iconIndex2);
        assertEquals(iconPath1, iconPath2);
        assertSame(iconIndex1, iconIndex2); // 同一个对象引用
        assertSame(iconPath1, iconPath2); // 同一个对象引用
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象不变性（在设置后）
     */
    @Test
    @DisplayName("不变性验证 - 对象状态不变性")
    void testObjectImmutabilityAfterSet() {
        // Arrange: 设置初始值
        iconExt.setIconIndex(50);
        iconExt.setIconPath("/path/to/final.png");

        // Act: 获取值并尝试修改（对于Integer和String，这不会影响原对象）
        Integer originalIndex = iconExt.getIconIndex();
        String originalPath = iconExt.getIconPath();

        // Assert: 验证原始值未被修改
        assertEquals(50, iconExt.getIconIndex());
        assertEquals("/path/to/final.png", iconExt.getIconPath());
        assertEquals(originalIndex, iconExt.getIconIndex());
        assertEquals(originalPath, iconExt.getIconPath());
    }
}
