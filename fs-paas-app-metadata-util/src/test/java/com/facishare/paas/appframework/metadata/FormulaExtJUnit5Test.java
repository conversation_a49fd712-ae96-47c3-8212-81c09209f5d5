package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.describe.Formula;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FormulaExt的JUnit 5测试类
 * 测试公式字段扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试公式字段扩展的创建、变更检测和表达式处理功能
 */
class FormulaExtJUnit5Test {

    private Formula mockFormula;
    private FormulaExt formulaExt;

    @BeforeEach
    void setUp() {
        mockFormula = mock(Formula.class);
        formulaExt = FormulaExt.of(mockFormula);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Act: 使用of方法创建实例
        FormulaExt result = FormulaExt.of(mockFormula);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockFormula, result.getFormula());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportFilter方法 - 支持过滤
     */
    @Test
    @DisplayName("过滤支持 - supportFilter方法支持过滤")
    void testSupportFilter_True() {
        // Arrange: 设置支持过滤的条件
        when(mockFormula.isIndex()).thenReturn(true);
        when(mockFormula.getFilterExpression()).thenReturn("");
        
        // Act: 执行supportFilter方法
        boolean result = formulaExt.supportFilter();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportFilter方法 - 不支持过滤（非索引）
     */
    @Test
    @DisplayName("过滤支持 - supportFilter方法不支持过滤（非索引）")
    void testSupportFilter_False_NotIndex() {
        // Arrange: 设置不支持过滤的条件（非索引）
        when(mockFormula.isIndex()).thenReturn(false);
        when(mockFormula.getFilterExpression()).thenReturn("");
        
        // Act: 执行supportFilter方法
        boolean result = formulaExt.supportFilter();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportFilter方法 - 不支持过滤（有过滤表达式）
     */
    @Test
    @DisplayName("过滤支持 - supportFilter方法不支持过滤（有过滤表达式）")
    void testSupportFilter_False_HasFilterExpression() {
        // Arrange: 设置不支持过滤的条件（有过滤表达式）
        when(mockFormula.isIndex()).thenReturn(true);
        when(mockFormula.getFilterExpression()).thenReturn("filter_expression");
        
        // Act: 执行supportFilter方法
        boolean result = formulaExt.supportFilter();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChanged方法 - 相同公式
     */
    @Test
    @DisplayName("变更检测 - isChanged方法相同公式")
    void testIsChanged_SameFormula() {
        // Arrange: 设置公式属性
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        when(mockFormula.getDefaultToZero()).thenReturn(true);
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getApiName()).thenReturn("test_formula");
        when(anotherFormula.getExpression()).thenReturn("1 + 1");
        when(anotherFormula.getDecimalPlaces()).thenReturn(2);
        when(anotherFormula.getDefaultToZero()).thenReturn(true);
        
        // Act: 执行isChanged方法
        boolean result = formulaExt.isChanged(anotherFormula);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChanged方法 - 不同公式
     */
    @Test
    @DisplayName("变更检测 - isChanged方法不同公式")
    void testIsChanged_DifferentFormula() {
        // Arrange: 设置公式属性
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        when(mockFormula.getDefaultToZero()).thenReturn(true);
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getApiName()).thenReturn("test_formula");
        when(anotherFormula.getExpression()).thenReturn("2 + 2"); // 不同表达式
        when(anotherFormula.getDecimalPlaces()).thenReturn(2);
        when(anotherFormula.getDefaultToZero()).thenReturn(true);
        
        // Act: 执行isChanged方法
        boolean result = formulaExt.isChanged(anotherFormula);
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isReturnTypeChanged方法 - 返回类型变更
     */
    @Test
    @DisplayName("变更检测 - isReturnTypeChanged方法返回类型变更")
    void testIsReturnTypeChanged_Changed() {
        // Arrange: 设置公式属性
        when(mockFormula.getReturnType()).thenReturn("NUMBER");
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getReturnType()).thenReturn("TEXT");
        
        // Act: 执行isReturnTypeChanged方法
        boolean result = formulaExt.isReturnTypeChanged(anotherFormula);
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isReturnTypeChanged方法 - 返回类型未变更
     */
    @Test
    @DisplayName("变更检测 - isReturnTypeChanged方法返回类型未变更")
    void testIsReturnTypeChanged_NotChanged() {
        // Arrange: 设置公式属性
        when(mockFormula.getReturnType()).thenReturn("NUMBER");
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getReturnType()).thenReturn("NUMBER");
        
        // Act: 执行isReturnTypeChanged方法
        boolean result = formulaExt.isReturnTypeChanged(anotherFormula);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isExpressionChanged方法 - 表达式变更
     */
    @Test
    @DisplayName("变更检测 - isExpressionChanged方法表达式变更")
    void testIsExpressionChanged_Changed() {
        // Arrange: 设置公式属性
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getExpression()).thenReturn("2 + 2");
        
        // Act: 执行isExpressionChanged方法
        boolean result = formulaExt.isExpressionChanged(anotherFormula);
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isExpressionChanged方法 - 表达式未变更
     */
    @Test
    @DisplayName("变更检测 - isExpressionChanged方法表达式未变更")
    void testIsExpressionChanged_NotChanged() {
        // Arrange: 设置公式属性
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getExpression()).thenReturn("1 + 1");
        
        // Act: 执行isExpressionChanged方法
        boolean result = formulaExt.isExpressionChanged(anotherFormula);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法 - 使用最后修改时间（直接引用）
     */
    @Test
    @DisplayName("最后修改时间 - usingLastModifiedTime方法直接引用")
    void testUsingLastModifiedTime_DirectReference() {
        // Arrange: 设置公式表达式
        when(mockFormula.getExpression()).thenReturn("$" + DBRecord.LAST_MODIFIED_TIME + "$");
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = formulaExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法 - 使用最后修改时间（间接引用）
     */
    @Test
    @DisplayName("最后修改时间 - usingLastModifiedTime方法间接引用")
    void testUsingLastModifiedTime_IndirectReference() {
        // Arrange: 设置公式表达式
        when(mockFormula.getExpression()).thenReturn("$account." + DBRecord.LAST_MODIFIED_TIME + "$");
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = formulaExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试usingLastModifiedTime方法 - 不使用最后修改时间
     */
    @Test
    @DisplayName("最后修改时间 - usingLastModifiedTime方法不使用")
    void testUsingLastModifiedTime_NotUsing() {
        // Arrange: 设置公式表达式
        when(mockFormula.getExpression()).thenReturn("$name$ + $amount$");
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = formulaExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法 - 相同对象
     */
    @Test
    @DisplayName("对象比较 - equals方法相同对象")
    void testEquals_SameObject() {
        // Act & Assert: 验证相同对象
        assertEquals(formulaExt, formulaExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法 - 相同公式
     */
    @Test
    @DisplayName("对象比较 - equals方法相同公式")
    void testEquals_SameFormula() {
        // Arrange: 设置公式属性
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        when(mockFormula.getDefaultToZero()).thenReturn(true);
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getApiName()).thenReturn("test_formula");
        when(anotherFormula.getExpression()).thenReturn("1 + 1");
        when(anotherFormula.getDecimalPlaces()).thenReturn(2);
        when(anotherFormula.getDefaultToZero()).thenReturn(true);
        
        FormulaExt anotherFormulaExt = FormulaExt.of(anotherFormula);
        
        // Act & Assert: 验证相同公式
        assertEquals(formulaExt, anotherFormulaExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals方法 - 不同公式
     */
    @Test
    @DisplayName("对象比较 - equals方法不同公式")
    void testEquals_DifferentFormula() {
        // Arrange: 设置公式属性
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        when(mockFormula.getDefaultToZero()).thenReturn(true);
        
        Formula anotherFormula = mock(Formula.class);
        when(anotherFormula.getApiName()).thenReturn("another_formula");
        when(anotherFormula.getExpression()).thenReturn("2 + 2");
        when(anotherFormula.getDecimalPlaces()).thenReturn(3);
        when(anotherFormula.getDefaultToZero()).thenReturn(false);
        
        FormulaExt anotherFormulaExt = FormulaExt.of(anotherFormula);
        
        // Act & Assert: 验证不同公式
        assertNotEquals(formulaExt, anotherFormulaExt);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hashCode方法
     */
    @Test
    @DisplayName("对象比较 - hashCode方法")
    void testHashCode() {
        // Arrange: 设置公式属性
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        
        // Act: 执行hashCode方法
        int result = formulaExt.hashCode();
        
        // Assert: 验证结果
        assertNotEquals(0, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockFormula.getApiName()).thenReturn("test_formula");
        when(mockFormula.getLabel()).thenReturn("Test Formula");
        when(mockFormula.getExpression()).thenReturn("1 + 1");
        when(mockFormula.getReturnType()).thenReturn("NUMBER");
        when(mockFormula.getDecimalPlaces()).thenReturn(2);
        
        // Act & Assert: 验证委托的方法
        assertEquals("test_formula", formulaExt.getApiName());
        assertEquals("Test Formula", formulaExt.getLabel());
        assertEquals("1 + 1", formulaExt.getExpression());
        assertEquals("NUMBER", formulaExt.getReturnType());
        assertEquals(2, formulaExt.getDecimalPlaces());
        
        // 验证委托调用
        verify(mockFormula).getApiName();
        verify(mockFormula).getLabel();
        verify(mockFormula).getExpression();
        verify(mockFormula).getReturnType();
        verify(mockFormula).getDecimalPlaces();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化表达式检测
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "$last_modified_time$",
        "$account.last_modified_time$",
        "$contact.last_modified_time$",
        "IF($last_modified_time$ > TODAY(), 1, 0)"
    })
    @DisplayName("参数化测试 - 最后修改时间表达式检测")
    void testUsingLastModifiedTime_ParameterizedExpressions(String expression) {
        // Arrange: 设置公式表达式
        when(mockFormula.getExpression()).thenReturn(expression);
        
        // Act: 执行usingLastModifiedTime方法
        boolean result = formulaExt.usingLastModifiedTime();
        
        // Assert: 验证结果
        assertTrue(result);
    }
}
