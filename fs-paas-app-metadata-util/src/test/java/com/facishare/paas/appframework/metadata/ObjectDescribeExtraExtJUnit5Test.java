package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribeExtra;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribeExtra;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ObjectDescribeExtraExt的JUnit 5测试类
 * 测试对象描述扩展的扩展功能
 * 
 * GenerateByAI
 * 测试内容描述：测试对象描述扩展的创建、属性获取和业务逻辑
 */
@ExtendWith(MockitoExtension.class)
class ObjectDescribeExtraExtJUnit5Test {

    @Mock
    private IObjectDescribeExtra mockObjectDescribeExtra;
    
    private ObjectDescribeExtraExt objectDescribeExtraExt;
    private Map<String, Object> testExtraMap;

    @BeforeEach
    void setUp() {
        // 创建测试用的对象描述扩展
        testExtraMap = new HashMap<>();
        testExtraMap.put("describe_api_name", "test_object");
        testExtraMap.put("icon_slot", 5);

        // 创建一个真实的ObjectDescribeExtra并设置iconIndex
        ObjectDescribeExtra extra = new ObjectDescribeExtra(testExtraMap);
        extra.setIconIndex(5); // 显式设置iconIndex

        objectDescribeExtraExt = ObjectDescribeExtraExt.of(extra);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - IObjectDescribeExtra参数
     */
    @Test
    @DisplayName("静态方法 - of工厂方法IObjectDescribeExtra参数")
    void testOf_WithIObjectDescribeExtra() {
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(mockObjectDescribeExtra);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockObjectDescribeExtra, result.getObjectDescribeExtra());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 避免双重包装
     */
    @Test
    @DisplayName("静态方法 - of工厂方法避免双重包装")
    void testOf_AvoidDoubleWrapping() {
        // Arrange: 创建ObjectDescribeExtraExt实例
        ObjectDescribeExtraExt originalExt = ObjectDescribeExtraExt.of(mockObjectDescribeExtra);

        // Act: 再次使用of方法包装
        // 注意：ObjectDescribeExtraExt类中没有直接接受ObjectDescribeExtraExt参数的of方法
        // 但是由于构造函数中有处理，我们可以通过获取内部的IObjectDescribeExtra来测试
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(originalExt.getObjectDescribeExtra());

        // Assert: 验证避免双重包装
        assertNotNull(result);
        assertSame(mockObjectDescribeExtra, result.getObjectDescribeExtra());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - Map参数
     */
    @Test
    @DisplayName("静态方法 - of工厂方法Map参数")
    void testOf_WithMap() {
        // Arrange: 准备Map参数
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("describe_api_name", "test_object");
        extraMap.put("icon_index", 5); // 使用icon_index而不是icon_slot

        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(extraMap);

        // Assert: 验证工厂方法
        assertNotNull(result);
        assertEquals("test_object", result.getDescribeApiName());
        assertEquals(5, result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 完整构建方法
     */
    @Test
    @DisplayName("静态方法 - of完整构建方法")
    void testOf_CompleteBuilder() {
        // Arrange: 准备参数
        String describeApiName = "test_object";
        IObjectDescribe objDesc = new ObjectDescribe();
        objDesc.setApiName(describeApiName);
        objDesc.setIconIndex(4);
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("icon_slot", 5);
        
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(describeApiName, objDesc, extraMap);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertEquals(5, result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 完整构建方法（自定义对象，无图标）
     */
    @Test
    @DisplayName("静态方法 - of完整构建方法自定义对象无图标")
    void testOf_CompleteBuilder_CustomObjectNoIcon() {
        // Arrange: 准备参数
        String describeApiName = "custom_object__c";
        IObjectDescribe objDesc = new ObjectDescribe();
        objDesc.setApiName(describeApiName);
        objDesc.setIconIndex(4);
        Map<String, Object> extraMap = new HashMap<>();
        
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(describeApiName, objDesc, extraMap);
        
        // Assert: 验证工厂方法 - 自定义对象无图标时应该使用objDesc的iconIndex+1
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertEquals(5, result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 完整构建方法（自定义对象，无图标，无描述）
     */
    @Test
    @DisplayName("静态方法 - of完整构建方法自定义对象无图标无描述")
    void testOf_CompleteBuilder_CustomObjectNoIconNoDesc() {
        // Arrange: 准备参数
        String describeApiName = "custom_object__c";
        Map<String, Object> extraMap = new HashMap<>();
        
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(describeApiName, null, extraMap);
        
        // Assert: 验证工厂方法 - 自定义对象无图标无描述时应该使用默认值1
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertEquals(1, result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 完整构建方法（非自定义对象）
     */
    @Test
    @DisplayName("静态方法 - of完整构建方法非自定义对象")
    void testOf_CompleteBuilder_NonCustomObject() {
        // Arrange: 准备参数
        String describeApiName = "standard_object";
        IObjectDescribe objDesc = new ObjectDescribe();
        objDesc.setApiName(describeApiName);
        objDesc.setIconIndex(4);
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("icon_slot", 5);
        
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(describeApiName, objDesc, extraMap);
        
        // Assert: 验证工厂方法 - 非自定义对象应该直接使用extraMap中的icon_slot
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertEquals(5, result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 完整构建方法（非自定义对象，无图标）
     */
    @Test
    @DisplayName("静态方法 - of完整构建方法非自定义对象无图标")
    void testOf_CompleteBuilder_NonCustomObjectNoIcon() {
        // Arrange: 准备参数
        String describeApiName = "standard_object";
        IObjectDescribe objDesc = new ObjectDescribe();
        objDesc.setApiName(describeApiName);
        objDesc.setIconIndex(4);
        Map<String, Object> extraMap = new HashMap<>();
        
        // Act: 使用of方法创建实例
        ObjectDescribeExtraExt result = ObjectDescribeExtraExt.of(describeApiName, objDesc, extraMap);
        
        // Assert: 验证工厂方法 - 非自定义对象无图标时应该返回null
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertNull(result.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIconSlot方法
     */
    @Test
    @DisplayName("获取方法 - getIconSlot方法")
    void testGetIconSlot() {
        // Act: 执行getIconSlot方法
        Integer result = objectDescribeExtraExt.getIconSlot();
        
        // Assert: 验证结果
        assertEquals(5, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托方法 - getDescribeApiName
     */
    @Test
    @DisplayName("委托方法 - getDescribeApiName")
    void testDelegateMethod_GetDescribeApiName() {
        // Act: 执行委托方法
        String result = objectDescribeExtraExt.getDescribeApiName();
        
        // Assert: 验证结果
        assertEquals("test_object", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托方法 - setDescribeApiName
     */
    @Test
    @DisplayName("委托方法 - setDescribeApiName")
    void testDelegateMethod_SetDescribeApiName() {
        // Arrange: 准备测试数据
        String newApiName = "new_test_object";
        
        // Act: 执行委托方法
        objectDescribeExtraExt.setDescribeApiName(newApiName);
        
        // Assert: 验证结果
        assertEquals(newApiName, objectDescribeExtraExt.getDescribeApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托方法 - getIconIndex
     */
    @Test
    @DisplayName("委托方法 - getIconIndex")
    void testDelegateMethod_GetIconIndex() {
        // Act: 执行委托方法
        Integer result = objectDescribeExtraExt.getIconIndex();
        
        // Assert: 验证结果
        assertEquals(5, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托方法 - setIconIndex
     */
    @Test
    @DisplayName("委托方法 - setIconIndex")
    void testDelegateMethod_SetIconIndex() {
        // Arrange: 准备测试数据
        Integer newIconIndex = 10;
        
        // Act: 执行委托方法
        objectDescribeExtraExt.setIconIndex(newIconIndex);
        
        // Assert: 验证结果
        assertEquals(newIconIndex, objectDescribeExtraExt.getIconIndex());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - null值处理
     */
    @Test
    @DisplayName("边界条件 - null值处理")
    void testNullValueHandling() {
        // Arrange: 准备测试数据
        when(mockObjectDescribeExtra.getIconIndex()).thenReturn(null);
        ObjectDescribeExtraExt nullExt = ObjectDescribeExtraExt.of(mockObjectDescribeExtra);
        
        // Act & Assert: 验证null值处理
        assertDoesNotThrow(() -> {
            Integer iconSlot = nullExt.getIconSlot();
            assertNull(iconSlot);
        });
    }
}
