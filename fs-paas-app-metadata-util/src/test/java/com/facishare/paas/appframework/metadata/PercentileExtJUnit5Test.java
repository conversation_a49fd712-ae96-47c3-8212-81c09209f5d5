package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.Percentile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PercentileExt的JUnit 5测试类
 * 测试百分比字段扩展类功能
 * 
 * GenerateByAI
 * 测试内容描述：测试百分比字段扩展的创建和配置获取功能
 */
class PercentileExtJUnit5Test {

    private Percentile mockPercentile;
    private PercentileExt percentileExt;

    @BeforeEach
    void setUp() {
        mockPercentile = mock(Percentile.class);
        percentileExt = PercentileExt.of(mockPercentile);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Act: 使用of方法创建实例
        PercentileExt result = PercentileExt.of(mockPercentile);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        // 由于没有getPercentile()方法，我们通过委托方法验证
        when(mockPercentile.getApiName()).thenReturn("test_api");
        assertEquals("test_api", result.getApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isAutoAdaptPlaces方法 - 默认值
     */
    @Test
    @DisplayName("配置获取 - isAutoAdaptPlaces默认值")
    void testIsAutoAdaptPlaces_DefaultValue() {
        // Arrange: 设置mock返回null（模拟未配置）
        when(mockPercentile.get("auto_adapt_places", Boolean.class, false)).thenReturn(false);
        
        // Act: 执行isAutoAdaptPlaces方法
        boolean result = percentileExt.isAutoAdaptPlaces();
        
        // Assert: 验证结果为默认值false
        assertFalse(result);
        verify(mockPercentile).get("auto_adapt_places", Boolean.class, false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isAutoAdaptPlaces方法 - 自定义值
     */
    @Test
    @DisplayName("配置获取 - isAutoAdaptPlaces自定义值")
    void testIsAutoAdaptPlaces_CustomValue() {
        // Arrange: 设置mock返回true
        when(mockPercentile.get("auto_adapt_places", Boolean.class, false)).thenReturn(true);
        
        // Act: 执行isAutoAdaptPlaces方法
        boolean result = percentileExt.isAutoAdaptPlaces();
        
        // Assert: 验证结果为配置的true
        assertTrue(result);
        verify(mockPercentile).get("auto_adapt_places", Boolean.class, false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDecimalPlaces方法 - 默认值
     */
    @Test
    @DisplayName("配置获取 - getDecimalPlaces默认值")
    void testGetDecimalPlaces_DefaultValue() {
        // Arrange: 设置mock返回默认值
        when(mockPercentile.get("decimal_places", Integer.class, 6)).thenReturn(6);
        
        // Act: 执行getDecimalPlaces方法
        int result = percentileExt.getDecimalPlaces();
        
        // Assert: 验证结果为默认值6
        assertEquals(6, result);
        verify(mockPercentile).get("decimal_places", Integer.class, 6);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDecimalPlaces方法 - 自定义值
     */
    @Test
    @DisplayName("配置获取 - getDecimalPlaces自定义值")
    void testGetDecimalPlaces_CustomValue() {
        // Arrange: 设置mock返回自定义值
        when(mockPercentile.get("decimal_places", Integer.class, 6)).thenReturn(4);
        
        // Act: 执行getDecimalPlaces方法
        int result = percentileExt.getDecimalPlaces();
        
        // Assert: 验证结果为配置的4
        assertEquals(4, result);
        verify(mockPercentile).get("decimal_places", Integer.class, 6);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLength方法 - 默认值
     */
    @Test
    @DisplayName("配置获取 - getLength默认值")
    void testGetLength_DefaultValue() {
        // Arrange: 设置mock返回默认值
        when(mockPercentile.get("length", Integer.class, 8)).thenReturn(8);
        
        // Act: 执行getLength方法
        int result = percentileExt.getLength();
        
        // Assert: 验证结果为默认值8
        assertEquals(8, result);
        verify(mockPercentile).get("length", Integer.class, 8);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLength方法 - 自定义值
     */
    @Test
    @DisplayName("配置获取 - getLength自定义值")
    void testGetLength_CustomValue() {
        // Arrange: 设置mock返回自定义值
        when(mockPercentile.get("length", Integer.class, 8)).thenReturn(10);
        
        // Act: 执行getLength方法
        int result = percentileExt.getLength();
        
        // Assert: 验证结果为配置的10
        assertEquals(10, result);
        verify(mockPercentile).get("length", Integer.class, 8);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMaxLength方法 - 默认值
     */
    @Test
    @DisplayName("配置获取 - getMaxLength默认值")
    void testGetMaxLength_DefaultValue() {
        // Arrange: 设置mock返回默认值
        when(mockPercentile.get("max_length", Integer.class, 14)).thenReturn(14);
        
        // Act: 执行getMaxLength方法
        int result = percentileExt.getMaxLength();
        
        // Assert: 验证结果为默认值14
        assertEquals(14, result);
        verify(mockPercentile).get("max_length", Integer.class, 14);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMaxLength方法 - 自定义值
     */
    @Test
    @DisplayName("配置获取 - getMaxLength自定义值")
    void testGetMaxLength_CustomValue() {
        // Arrange: 设置mock返回自定义值
        when(mockPercentile.get("max_length", Integer.class, 14)).thenReturn(20);
        
        // Act: 执行getMaxLength方法
        int result = percentileExt.getMaxLength();
        
        // Assert: 验证结果为配置的20
        assertEquals(20, result);
        verify(mockPercentile).get("max_length", Integer.class, 14);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化配置获取
     */
    @ParameterizedTest
    @CsvSource({
        "auto_adapt_places,Boolean,false,true",
        "decimal_places,Integer,6,3",
        "length,Integer,8,12",
        "max_length,Integer,14,18"
    })
    @DisplayName("参数化测试 - 配置获取")
    void testParameterizedConfigGet(String key, String type, String defaultValue, String customValue) {
        // Arrange: 根据参数类型设置mock
        if ("Boolean".equals(type)) {
            when(mockPercentile.get(key, Boolean.class, Boolean.parseBoolean(defaultValue)))
                    .thenReturn(Boolean.parseBoolean(customValue));
        } else if ("Integer".equals(type)) {
            when(mockPercentile.get(key, Integer.class, Integer.parseInt(defaultValue)))
                    .thenReturn(Integer.parseInt(customValue));
        }
        
        // Act & Assert: 根据参数类型执行不同的方法并验证
        switch (key) {
            case "auto_adapt_places":
                assertEquals(Boolean.parseBoolean(customValue), percentileExt.isAutoAdaptPlaces());
                break;
            case "decimal_places":
                assertEquals(Integer.parseInt(customValue), percentileExt.getDecimalPlaces());
                break;
            case "length":
                assertEquals(Integer.parseInt(customValue), percentileExt.getLength());
                break;
            case "max_length":
                assertEquals(Integer.parseInt(customValue), percentileExt.getMaxLength());
                break;
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockPercentile.getApiName()).thenReturn("test_percentile");
        when(mockPercentile.getLabel()).thenReturn("Test Percentile");
        when(mockPercentile.isRequired()).thenReturn(true);
        
        // Act & Assert: 验证委托的方法
        assertEquals("test_percentile", percentileExt.getApiName());
        assertEquals("Test Percentile", percentileExt.getLabel());
        assertTrue(percentileExt.isRequired());
        
        // 验证委托调用
        verify(mockPercentile).getApiName();
        verify(mockPercentile).getLabel();
        verify(mockPercentile).isRequired();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 设置toString返回值
        when(mockPercentile.toString()).thenReturn("Percentile[apiName=test_percentile]");
        
        // Act: 调用toString方法
        String result = percentileExt.toString();
        
        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("Percentile"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建两个使用相同Percentile的实例
        when(mockPercentile.getApiName()).thenReturn("test_percentile");

        PercentileExt ext1 = PercentileExt.of(mockPercentile);
        PercentileExt ext2 = PercentileExt.of(mockPercentile);

        // Assert: 验证equals和hashCode
        // 由于没有getPercentile()方法，我们通过委托方法验证
        assertEquals(ext1.getApiName(), ext2.getApiName());

        // 创建使用不同Percentile的实例
        Percentile anotherPercentile = mock(Percentile.class);
        when(anotherPercentile.getApiName()).thenReturn("another_percentile");

        PercentileExt ext3 = PercentileExt.of(anotherPercentile);

        assertNotEquals(ext1.getApiName(), ext3.getApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null参数处理
     */
    @Test
    @DisplayName("边界条件 - null参数处理")
    void testNullParameter() {
        // Act: 创建使用null参数的实例
        PercentileExt result = PercentileExt.of(null);

        // Assert: 验证实例创建成功，但使用委托方法时会抛出异常
        assertNotNull(result);
        assertThrows(NullPointerException.class, () -> {
            result.getApiName(); // 尝试调用委托方法会抛出NullPointerException
        });
    }
}
