//package com.facishare.paas.appframework.metadata.mtresource
//
//import com.facishare.paas.appframework.common.util.AppFrameworkConfig
//import com.facishare.paas.appframework.common.util.CollectionUtils
//import com.facishare.paas.appframework.core.model.User
//import com.facishare.paas.appframework.metadata.DescribeLogicService
//import com.facishare.paas.appframework.metadata.repository.api.IRepository
//import com.facishare.paas.appframework.metadata.repository.model.MtResource
//import com.facishare.paas.appframework.metadata.search.Query
//import com.facishare.paas.metadata.api.describe.IObjectDescribe
//import com.fxiaoke.i18n.client.I18nClient
//import com.fxiaoke.i18n.client.impl.I18nServiceImpl
//import org.powermock.reflect.Whitebox
//import spock.lang.Specification
//import spock.lang.Unroll
//
//@Unroll
//class MtResourceServiceImplTest extends Specification {
//
//    MtResourceServiceImpl mtResourceService
//    def repository = Mock(IRepository)
//    def describeLogicService = Mock(DescribeLogicService)
//    def user = Mock(User)
//    def objectDescribe = Mock(IObjectDescribe)
//
//    def setupSpec() {
//        def i18nClient = Mock(I18nClient)
//        def i18nServiceImpl = Mock(I18nServiceImpl)
//        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
//        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
//        i18nClient.getAllLanguage() >> []
//    }
//
//    def setup() {
//        mtResourceService = new MtResourceServiceImpl()
//        mtResourceService.repository = repository
//        mtResourceService.describeLogicService = describeLogicService
//
//        user.getTenantId() >> "tenant123"
//        user.getUserId() >> "user123"
//        objectDescribe.getApiName() >> "TestObj"
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试查询资源的正常场景
//     */
//    def "queryResourceTest - 正常查询资源"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1", "field2"]
//
//        def mtResource1 = Mock(MtResource)
//        def mtResource2 = Mock(MtResource)
//        def expectedResult = [mtResource1, mtResource2]
//
//        when:
//        def result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues)
//
//        then:
//        result == expectedResult
//        1 * repository.findBy(_, _, MtResource.class) >> expectedResult
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试查询资源时资源值为空的场景
//     */
//    def "queryResourceTest - 资源值为空"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = []
//
//        when:
//        def result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues)
//
//        then:
//        result == []
//        1 * repository.findBy(_, _, MtResource.class) >> []
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试查询资源时资源值为null的场景
//     */
//    def "queryResourceTest - 资源值为null"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        List<String> resourceValues = null
//
//        when:
//        def result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues)
//
//        then:
//        result == []
//        1 * repository.findBy(_, _, MtResource.class) >> []
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试带源类型和源值查询资源的正常场景
//     */
//    def "queryResourceTest - 带源类型和源值查询"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1"]
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//
//        def mtResource = Mock(MtResource)
//
//        when:
//        def result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType, sourceValue)
//
//        then:
//        result == [mtResource]
//        1 * repository.findBy(_, _, MtResource.class) >> [mtResource]
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型查询资源的正常场景
//     */
//    def "queryResourceByResourceTypesTest - 正常查询"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceTypes = ["field", "button"]
//        def resourceValue = "test"
//
//        def mtResource = Mock(MtResource)
//
//        when:
//        def result = mtResourceService.queryResourceByResourceTypes(tenantId, resourceParentValue, resourceTypes, resourceValue)
//
//        then:
//        result == [mtResource]
//        1 * repository.findBy(_, _, MtResource.class) >> [mtResource]
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型查询资源时资源类型为空的场景
//     */
//    def "queryResourceByResourceTypesTest - 资源类型为空"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceTypes = []
//        def resourceValue = "test"
//
//        when:
//        def result = mtResourceService.queryResourceByResourceTypes(tenantId, resourceParentValue, resourceTypes, resourceValue)
//
//        then:
//        result == []
//        1 * repository.findBy(_, _, MtResource.class) >> []
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试修改资源的正常场景，资源不存在需要创建
//     */
//    def "modifyResourceTest - 资源不存在需要创建"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1", "field2"]
//        def controlLevel = "tenant"
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//
//        def createdResources = [Mock(MtResource), Mock(MtResource)]
//
//        when:
//        def result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, resourceValues, controlLevel, sourceType, sourceValue)
//
//        then:
//        result == createdResources
//        1 * repository.findBy(_, _, MtResource.class) >> []
//        1 * repository.bulkCreate(_, _) >> createdResources
//        0 * repository.bulkUpsert(_, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试修改资源的正常场景，资源存在需要更新
//     */
//    def "modifyResourceTest - 资源存在需要更新"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1", "field2"]
//        def controlLevel = "tenant"
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//
//        def existingResource = Mock(MtResource)
//        existingResource.getResourceValue() >> "field1"
//        def updatedResources = [existingResource, Mock(MtResource)]
//
//        when:
//        def result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, resourceValues, controlLevel, sourceType, sourceValue)
//
//        then:
//        result == updatedResources
//        1 * repository.findBy(_, _, MtResource.class) >> [existingResource]
//        1 * existingResource.setControlLevel(controlLevel)
//        1 * existingResource.setSourceValue(sourceValue)
//        1 * repository.bulkUpsert(_, _) >> updatedResources
//        0 * repository.bulkCreate(_, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试修改资源时资源值为空的场景
//     */
//    def "modifyResourceTest - 资源值为空"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = []
//        def controlLevel = "tenant"
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//
//        when:
//        def result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, resourceValues, controlLevel, sourceType, sourceValue)
//
//        then:
//        result == []
//        0 * repository.findBy(_, _, _)
//        0 * repository.bulkCreate(_, _)
//        0 * repository.bulkUpsert(_, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试修改资源时资源值为null的场景
//     */
//    def "modifyResourceTest - 资源值为null"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        List<String> resourceValues = null
//        def controlLevel = "tenant"
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//
//        when:
//        def result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, resourceValues, controlLevel, sourceType, sourceValue)
//
//        then:
//        result == []
//        0 * repository.findBy(_, _, _)
//        0 * repository.bulkCreate(_, _)
//        0 * repository.bulkUpsert(_, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试禁用资源的正常场景
//     */
//    def "disableResourceTest - 正常禁用资源"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1", "field2"]
//        def sourceType = "mt_describe"
//
//        def mtResource1 = Mock(MtResource)
//        def mtResource2 = Mock(MtResource)
//
//        when:
//        mtResourceService.disableResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType)
//
//        then:
//        1 * repository.findBy(_, _, MtResource.class) >> [mtResource1, mtResource2]
//        1 * mtResource1.setStatus(MtResource.STATUS_TYPE_DISABLE)
//        1 * mtResource2.setStatus(MtResource.STATUS_TYPE_DISABLE)
//        1 * repository.bulkUpsert(_, [mtResource1, mtResource2])
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试禁用资源时找不到资源的场景
//     */
//    def "disableResourceTest - 找不到资源"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def resourceValues = ["field1"]
//        def sourceType = "mt_describe"
//
//        when:
//        mtResourceService.disableResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType)
//
//        then:
//        1 * repository.findBy(_, _, MtResource.class) >> []
//        0 * repository.bulkUpsert(_, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试更新描述并修改资源的正常场景
//     */
//    def "updateDescribeAndModifyResourceTest - 正常更新"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceValues = ["field1", "field2"]
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//        def controlLevel = "tenant"
//
//        def createdResources = [Mock(MtResource), Mock(MtResource)]
//
//        when:
//        mtResourceService.updateDescribeAndModifyResource(tenantId, objectDescribe, resourceValues, sourceType, sourceValue, controlLevel)
//
//        then:
//        1 * describeLogicService.update(objectDescribe)
//        1 * repository.findBy(_, _, MtResource.class) >> []
//        1 * repository.bulkCreate(_, _) >> createdResources
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试更新描述并修改资源时描述更新失败的场景
//     */
//    def "updateDescribeAndModifyResourceError - 描述更新失败"() {
//        given:
//        def tenantId = "tenant123"
//        def resourceValues = ["field1"]
//        def sourceType = "mt_describe"
//        def sourceValue = "TestObj"
//        def controlLevel = "tenant"
//
//        when:
//        mtResourceService.updateDescribeAndModifyResource(tenantId, objectDescribe, resourceValues, sourceType, sourceValue, controlLevel)
//
//        then:
//        thrown(RuntimeException)
//        1 * describeLogicService.update(objectDescribe) >> { throw new RuntimeException("Update failed") }
//        0 * repository.findBy(_, _, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型统计数量的正常场景
//     */
//    def "countByResourceTypeTest - 正常统计"() {
//        given:
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def expectedCount = 5
//
//        when:
//        def result = mtResourceService.countByResourceType(user, resourceParentValue, resourceType)
//
//        then:
//        result == expectedCount
//        1 * repository.findCountOnly(user, _, MtResource.class) >> expectedCount
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型统计数量时结果为0的场景
//     */
//    def "countByResourceTypeTest - 统计结果为0"() {
//        given:
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        def expectedCount = 0
//
//        when:
//        def result = mtResourceService.countByResourceType(user, resourceParentValue, resourceType)
//
//        then:
//        result == expectedCount
//        1 * repository.findCountOnly(user, _, MtResource.class) >> expectedCount
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型统计数量时用户为null的场景
//     */
//    def "countByResourceTypeError - 用户为null"() {
//        given:
//        def resourceParentValue = "TestObj"
//        def resourceType = "field"
//        User nullUser = null
//
//        when:
//        mtResourceService.countByResourceType(nullUser, resourceParentValue, resourceType)
//
//        then:
//        thrown(NullPointerException)
//        0 * repository.findCountOnly(_, _, _)
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型统计数量时资源父值为null的场景
//     */
//    def "countByResourceTypeTest - 资源父值为null"() {
//        given:
//        String resourceParentValue = null
//        def resourceType = "field"
//        def expectedCount = 0
//
//        when:
//        def result = mtResourceService.countByResourceType(user, resourceParentValue, resourceType)
//
//        then:
//        result == expectedCount
//        1 * repository.findCountOnly(user, _, MtResource.class) >> expectedCount
//    }
//
//    /**
//     * GenerateByAI
//     * 测试内容描述：测试根据资源类型统计数量时资源类型为null的场景
//     */
//    def "countByResourceTypeTest - 资源类型为null"() {
//        given:
//        def resourceParentValue = "TestObj"
//        String resourceType = null
//        def expectedCount = 10
//
//        when:
//        def result = mtResourceService.countByResourceType(user, resourceParentValue, resourceType)
//
//        then:
//        result == expectedCount
//        1 * repository.findCountOnly(user, _, MtResource.class) >> expectedCount
//    }
//}
