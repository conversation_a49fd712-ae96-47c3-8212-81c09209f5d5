package com.facishare.paas.appframework.metadata.menu

import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.MetaDataActionService
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class MenuCommonServiceImpl1Test extends Specification {

    MenuCommonServiceImpl menuCommonService
    def describeLogicService = Mock(DescribeLogicService)
    def metaDataFindService = Mock(MetaDataFindService)
    def metaDataActionService = Mock(MetaDataActionService)
    def user = Mock(User)
    def menuItemDescribe = Mock(IObjectDescribe)
    def menuObjectData = Mock(IObjectData)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        menuCommonService = new MenuCommonServiceImpl()
        menuCommonService.describeLogicService = describeLogicService
        menuCommonService.metaDataFindService = metaDataFindService
        menuCommonService.metaDataActionService = metaDataActionService
        
        // 模拟静态字段
        def configMenuItemMap = [:]
        configMenuItemMap.put("TestObj", new MenuItemConfigObject(true, true, "web"))
        Whitebox.setInternalState(MenuConstants, "configMenuItemMap", configMenuItemMap)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建菜单项的正常场景
     */
    def "createMenuItemTest - 正常创建菜单项"() {
        given:
        def tenantId = "tenant123"
        def apiName = "TestObj"
        def menuId = "menu123"
        
        user.getTenantId() >> tenantId
        describeLogicService.findObject(tenantId, MenuConstants.MENU_ITEM_API_NAME) >> menuItemDescribe
        
        def systemCrmMenu = Mock(IObjectData)
        systemCrmMenu.getId() >> menuId
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [systemCrmMenu])
        
        // 模拟没有找到现有菜单项
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [])
        
        def lastMenuItem = Mock(IObjectData)
        lastMenuItem.get(MenuConstants.MenuItemField.NUMBER.getApiName(), Integer.class) >> 10
        
        def newMenuItem = Mock(IObjectData)
        menuItemDescribe.getId() >> "describe123"
        menuItemDescribe.getApiName() >> MenuConstants.MENU_ITEM_API_NAME
        
        when:
        def result = menuCommonService.createMenuItem(user, apiName)
        
        then:
        1 * metaDataActionService.saveObjectData(user, _)
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建菜单项时菜单项已存在的场景
     */
    def "createMenuItemTest - 菜单项已存在"() {
        given:
        def tenantId = "tenant123"
        def apiName = "TestObj"
        def menuId = "menu123"
        
        user.getTenantId() >> tenantId
        describeLogicService.findObject(tenantId, MenuConstants.MENU_ITEM_API_NAME) >> menuItemDescribe
        
        def systemCrmMenu = Mock(IObjectData)
        systemCrmMenu.getId() >> menuId
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [systemCrmMenu])
        
        def existingMenuItem = Mock(IObjectData)
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [existingMenuItem])
        
        when:
        def result = menuCommonService.createMenuItem(user, apiName)
        
        then:
        0 * metaDataActionService.saveObjectData(_, _)
        result == existingMenuItem
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新菜单项的正常场景
     */
    def "createOrUpdateMenuItemWithOrderTest - 正常创建菜单项"() {
        given:
        def tenantId = "tenant123"
        def apiName = "TestObj"
        def order = 15
        def menuId = "menu123"
        
        user.getTenantId() >> tenantId
        describeLogicService.findObject(tenantId, MenuConstants.MENU_ITEM_API_NAME) >> menuItemDescribe
        
        def systemCrmMenu = Mock(IObjectData)
        systemCrmMenu.getId() >> menuId
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [systemCrmMenu])
        
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [])
        
        menuItemDescribe.getId() >> "describe123"
        menuItemDescribe.getApiName() >> MenuConstants.MENU_ITEM_API_NAME
        
        when:
        def result = menuCommonService.createOrUpdateMenuItemWithOrder(user, apiName, order)
        
        then:
        1 * metaDataActionService.saveObjectData(user, _)
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新菜单项时菜单项已存在的场景
     */
    def "createOrUpdateMenuItemWithOrderTest - 更新现有菜单项"() {
        given:
        def tenantId = "tenant123"
        def apiName = "TestObj"
        def order = 20
        def menuId = "menu123"
        
        user.getTenantId() >> tenantId
        describeLogicService.findObject(tenantId, MenuConstants.MENU_ITEM_API_NAME) >> menuItemDescribe
        
        def systemCrmMenu = Mock(IObjectData)
        systemCrmMenu.getId() >> menuId
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [systemCrmMenu])
        
        def existingMenuItem = Mock(IObjectData)
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [existingMenuItem])
        
        when:
        def result = menuCommonService.createOrUpdateMenuItemWithOrder(user, apiName, order)
        
        then:
        1 * existingMenuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), order)
        1 * metaDataActionService.updateObjectData(user, existingMenuItem)
        result == existingMenuItem
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新菜单项时order为null的异常场景
     */
    def "createOrUpdateMenuItemWithOrderError - order为null"() {
        given:
        def tenantId = "tenant123"
        def apiName = "TestObj"
        Integer order = null
        
        user.getTenantId() >> tenantId
        
        when:
        menuCommonService.createOrUpdateMenuItemWithOrder(user, apiName, order)
        
        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找默认CRM菜单的正常场景
     */
    def "findDefaultCrmMenuTest - 正常查找"() {
        given:
        def systemCrmMenu = Mock(IObjectData)
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [systemCrmMenu])
        
        when:
        def result = menuCommonService.findDefaultCrmMenu(user)
        
        then:
        result == systemCrmMenu
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找默认CRM菜单时未找到的场景
     */
    def "findDefaultCrmMenuTest - 未找到菜单"() {
        given:
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [])
        
        when:
        def result = menuCommonService.findDefaultCrmMenu(user)
        
        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找菜单项的正常场景
     */
    def "findMenuItemByApiNameTest - 正常查找"() {
        given:
        def menuId = "menu123"
        def apiNames = ["TestObj1", "TestObj2"]
        def menuItem1 = Mock(IObjectData)
        def menuItem2 = Mock(IObjectData)
        
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [menuItem1, menuItem2])
        
        when:
        def result = menuCommonService.findMenuItemByApiName(user, menuId, apiNames)
        
        then:
        result.size() == 2
        result.contains(menuItem1)
        result.contains(menuItem2)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找菜单项时未找到的场景
     */
    def "findMenuItemByApiNameTest - 未找到菜单项"() {
        given:
        def menuId = "menu123"
        def apiNames = ["NonExistentObj"]
        
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [])
        
        when:
        def result = menuCommonService.findMenuItemByApiName(user, menuId, apiNames)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找最后一个CRM菜单项的正常场景
     */
    def "findLastCrmMenuItemTest - 正常查找"() {
        given:
        def menuId = "menu123"
        def lastMenuItem = Mock(IObjectData)
        
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [lastMenuItem])
        
        when:
        def result = menuCommonService.findLastCrmMenuItem(user, menuId)
        
        then:
        result == lastMenuItem
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找最后一个CRM菜单项时未找到的场景
     */
    def "findLastCrmMenuItemTest - 未找到菜单项"() {
        given:
        def menuId = "menu123"
        
        metaDataFindService.findBySearchQuery(user, MenuConstants.MENU_ITEM_API_NAME, _) >> 
            new QueryResult<IObjectData>(data: [])
        
        when:
        def result = menuCommonService.findLastCrmMenuItem(user, menuId)
        
        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建菜单项的正常场景（有配置）
     */
    def "buildMenuItemTest - 有配置的对象"() {
        given:
        def tenantId = "tenant123"
        def menuId = "menu123"
        def objectApiName = "TestObj"
        
        user.getTenantId() >> tenantId
        menuItemDescribe.getId() >> "describe123"
        menuItemDescribe.getApiName() >> MenuConstants.MENU_ITEM_API_NAME
        
        when:
        def result = menuCommonService.buildMenuItem(user, menuId, menuItemDescribe, objectApiName)
        
        then:
        result != null
        result.getTenantId() == tenantId
        result.getDescribeId() == "describe123"
        result.getDescribeApiName() == MenuConstants.MENU_ITEM_API_NAME
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建菜单项的正常场景（无配置）
     */
    def "buildMenuItemTest - 无配置的对象"() {
        given:
        def tenantId = "tenant123"
        def menuId = "menu123"
        def objectApiName = "UnknownObj"
        
        user.getTenantId() >> tenantId
        menuItemDescribe.getId() >> "describe123"
        menuItemDescribe.getApiName() >> MenuConstants.MENU_ITEM_API_NAME
        
        when:
        def result = menuCommonService.buildMenuItem(user, menuId, menuItemDescribe, objectApiName)
        
        then:
        result != null
        result.getTenantId() == tenantId
        result.getDescribeId() == "describe123"
        result.getDescribeApiName() == MenuConstants.MENU_ITEM_API_NAME
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建菜单项时用户为null的边界场景
     */
    def "buildMenuItemError - 用户为null"() {
        given:
        def menuId = "menu123"
        def objectApiName = "TestObj"
        User nullUser = null
        
        menuItemDescribe.getId() >> "describe123"
        menuItemDescribe.getApiName() >> MenuConstants.MENU_ITEM_API_NAME
        
        when:
        def result = menuCommonService.buildMenuItem(nullUser, menuId, menuItemDescribe, objectApiName)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建菜单项时menuItemDescribe为null的边界场景
     */
    def "buildMenuItemError - menuItemDescribe为null"() {
        given:
        def tenantId = "tenant123"
        def menuId = "menu123"
        def objectApiName = "TestObj"
        IObjectDescribe nullDescribe = null
        
        user.getTenantId() >> tenantId
        
        when:
        def result = menuCommonService.buildMenuItem(user, menuId, nullDescribe, objectApiName)
        
        then:
        thrown(NullPointerException)
    }
} 