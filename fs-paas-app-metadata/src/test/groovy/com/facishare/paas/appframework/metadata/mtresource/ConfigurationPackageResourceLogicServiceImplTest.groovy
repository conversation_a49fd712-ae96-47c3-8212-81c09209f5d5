package com.facishare.paas.appframework.metadata.mtresource

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource
import com.facishare.paas.appframework.metadata.mtresource.model.FieldControlInfoHelper
import com.facishare.paas.appframework.metadata.mtresource.model.ResourceQuery
import com.facishare.paas.appframework.metadata.repository.model.MtResource
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class ConfigurationPackageResourceLogicServiceImplTest extends Specification {

    ConfigurationPackageResourceLogicServiceImpl configurationPackageResourceLogicService
    def mtResourceService = Mock(IMtResourceService)
    def user = Mock(User)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        configurationPackageResourceLogicService = new ConfigurationPackageResourceLogicServiceImpl()
        configurationPackageResourceLogicService.setMtResourceService(mtResourceService)
        
        user.getTenantId() >> "tenant123"
        user.getUserId() >> "user123"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找配置包资源的正常场景
     */
    def "findTest - 正常查找配置包资源"() {
        given:
        def resourceParentValue = "TestObj"
        def resourceType = "field"
        def resourceQuery = Mock(ResourceQuery)
        def sourceType = "mt_describe"
        def sourceValue = "TestObj"
        
        def resourceValue1 = ConfigurationPackageResource.ResourceValue.of("key1", "value1")
        def resourceValue2 = ConfigurationPackageResource.ResourceValue.of("key2", "value2")
        def resourceValues = ["key1\u0003key2": [resourceValue1, resourceValue2]]
        def keys = Sets.newHashSet("key1", "key2")
        
        resourceQuery.mackResourceValues() >> resourceValues
        resourceQuery.getKeys() >> keys
        
        def mtResource = Mock(MtResource)
        mtResource.getResourceValue() >> "key1\u0003key2"
        mtResource.getResourceParentValue() >> resourceParentValue
        mtResource.getResourceType() >> resourceType
        mtResource.getControlLevel() >> "tenant"
        mtResource.getSourceType() >> sourceType
        mtResource.getSourceValue() >> sourceValue
        
        mtResourceService.queryResource(user.getTenantId(), resourceParentValue, resourceType, ["key1\u0003key2"], sourceType, sourceValue) >> [mtResource]
        
        when:
        def result = configurationPackageResourceLogicService.find(user, resourceParentValue, resourceType, resourceQuery, sourceType, sourceValue)
        
        then:
        result.size() == 1
        result[0].resourceParentValue == resourceParentValue
        result[0].resourceType == resourceType
        result[0].controlLevel == "tenant"
        result[0].sourceType == sourceType
        result[0].sourceValue == sourceValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找配置包资源时资源查询为空的场景
     */
    def "findTest - 资源查询为空"() {
        given:
        def resourceParentValue = "TestObj"
        def resourceType = "field"
        def resourceQuery = Mock(ResourceQuery)
        def sourceType = "mt_describe"
        def sourceValue = "TestObj"
        
        resourceQuery.mackResourceValues() >> [:]
        resourceQuery.getKeys() >> Sets.newHashSet()
        
        mtResourceService.queryResource(user.getTenantId(), resourceParentValue, resourceType, [], sourceType, sourceValue) >> []
        
        when:
        def result = configurationPackageResourceLogicService.find(user, resourceParentValue, resourceType, resourceQuery, sourceType, sourceValue)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找配置包资源时资源父值为空的场景
     */
    def "findTest - 资源父值为空"() {
        given:
        String resourceParentValue = null
        def resourceType = "field"
        def resourceQuery = Mock(ResourceQuery)
        def sourceType = "mt_describe"
        def sourceValue = "TestObj"
        
        def resourceValues = ["key1": [ConfigurationPackageResource.ResourceValue.of("key1", "value1")]]
        def keys = Sets.newHashSet("key1")
        
        resourceQuery.mackResourceValues() >> resourceValues
        resourceQuery.getKeys() >> keys
        
        mtResourceService.queryResource(user.getTenantId(), ConfigurationPackageResource.EMPTY, resourceType, ["key1"], sourceType, sourceValue) >> []
        
        when:
        def result = configurationPackageResourceLogicService.find(user, resourceParentValue, resourceType, resourceQuery, sourceType, sourceValue)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型查找配置包资源的正常场景
     */
    def "findByResourceTypesTest - 正常查找"() {
        given:
        def resourceParentValue = "TestObj"
        def resourceTypes = ["field", "button"]
        def describeApiName = "TestObj"
        
        def mtResource = Mock(MtResource)
        mtResource.getResourceValue() >> "field1"
        mtResource.getResourceParentValue() >> resourceParentValue
        mtResource.getResourceType() >> "field"
        mtResource.getControlLevel() >> "tenant"
        mtResource.getSourceType() >> "mt_describe"
        mtResource.getSourceValue() >> describeApiName
        
        mtResourceService.queryResourceByResourceTypes(user.getTenantId(), resourceParentValue, resourceTypes, describeApiName) >> [mtResource]
        
        when:
        def result = configurationPackageResourceLogicService.findByResourceTypes(user, resourceParentValue, resourceTypes, describeApiName)
        
        then:
        result.size() == 1
        result[0].resourceParentValue == resourceParentValue
        result[0].resourceType == "field"
        result[0].controlLevel == "tenant"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型查找配置包资源时资源类型为空的场景
     */
    def "findByResourceTypesTest - 资源类型为空"() {
        given:
        def resourceParentValue = "TestObj"
        def resourceTypes = []
        def describeApiName = "TestObj"
        
        mtResourceService.queryResourceByResourceTypes(user.getTenantId(), resourceParentValue, resourceTypes, describeApiName) >> []
        
        when:
        def result = configurationPackageResourceLogicService.findByResourceTypes(user, resourceParentValue, resourceTypes, describeApiName)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型查找配置包资源时资源父值为null的场景
     */
    def "findByResourceTypesTest - 资源父值为null"() {
        given:
        String resourceParentValue = null
        def resourceTypes = ["field"]
        def describeApiName = "TestObj"
        
        mtResourceService.queryResourceByResourceTypes(user.getTenantId(), ConfigurationPackageResource.EMPTY, resourceTypes, describeApiName) >> []
        
        when:
        def result = configurationPackageResourceLogicService.findByResourceTypes(user, resourceParentValue, resourceTypes, describeApiName)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源的正常场景
     */
    def "modifyResourceTest - 正常修改资源"() {
        given:
        def resource1 = Mock(ConfigurationPackageResource)
        def resource2 = Mock(ConfigurationPackageResource)
        def resources = [resource1, resource2]
        
        def groupKey = "TestObj\u0003field\u0003tenant\u0003mt_describe\u0003TestObj"
        resource1.groupByKey() >> groupKey
        resource2.groupByKey() >> groupKey
        resource1.getResourceParentValue() >> "TestObj"
        resource1.getResourceType() >> "field"
        resource1.getControlLevel() >> "tenant"
        resource1.getSourceType() >> "mt_describe"
        resource1.getSourceValue() >> "TestObj"
        resource1.getResourceValue() >> "field1"
        resource2.getResourceValue() >> "field2"
        
        mtResourceService.modifyResource(user.getTenantId(), "TestObj", "field", ["field1", "field2"], "tenant", "mt_describe", "TestObj") >> []
        
        when:
        configurationPackageResourceLogicService.modifyResource(user, resources)
        
        then:
        1 * mtResourceService.modifyResource(user.getTenantId(), "TestObj", "field", ["field1", "field2"], "tenant", "mt_describe", "TestObj")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源时资源集合为空的场景
     */
    def "modifyResourceTest - 资源集合为空"() {
        given:
        def resources = []
        
        when:
        configurationPackageResourceLogicService.modifyResource(user, resources)
        
        then:
        0 * mtResourceService.modifyResource(_, _, _, _, _, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源时资源集合为null的场景
     */
    def "modifyResourceTest - 资源集合为null"() {
        given:
        Collection<ConfigurationPackageResource> resources = null
        
        when:
        configurationPackageResourceLogicService.modifyResource(user, resources)
        
        then:
        0 * mtResourceService.modifyResource(_, _, _, _, _, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型统计数量的正常场景
     */
    def "countByResourceTypeTest - 正常统计"() {
        given:
        def resourceParentValue = "TestObj"
        def resourceType = "field"
        def expectedCount = 5
        
        mtResourceService.countByResourceType(user, resourceParentValue, resourceType) >> expectedCount
        
        when:
        def result = configurationPackageResourceLogicService.countByResourceType(user, resourceParentValue, resourceType)
        
        then:
        result == expectedCount
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型统计数量时资源父值为null的场景
     */
    def "countByResourceTypeTest - 资源父值为null"() {
        given:
        String resourceParentValue = null
        def resourceType = "field"
        def expectedCount = 3
        
        mtResourceService.countByResourceType(user, ConfigurationPackageResource.EMPTY, resourceType) >> expectedCount
        
        when:
        def result = configurationPackageResourceLogicService.countByResourceType(user, resourceParentValue, resourceType)
        
        then:
        result == expectedCount
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找字段控制配置的正常场景
     */
    def "findFieldControlConfigsTest - 正常查找"() {
        given:
        def describeApiName = "TestObj"
        
        def mtResource = Mock(MtResource)
        mtResourceService.queryResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, null, MtResource.SOURCE_TYPE_FIELD_CONTROL, null) >> [mtResource]
        
        when:
        def result = configurationPackageResourceLogicService.findFieldControlConfigs(user, describeApiName)
        
        then:
        result == [mtResource]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找字段控制配置时找不到配置的场景
     */
    def "findFieldControlConfigsTest - 找不到配置"() {
        given:
        def describeApiName = "TestObj"
        
        mtResourceService.queryResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, null, MtResource.SOURCE_TYPE_FIELD_CONTROL, null) >> []
        
        when:
        def result = configurationPackageResourceLogicService.findFieldControlConfigs(user, describeApiName)
        
        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置的正常场景
     */
    def "updateFieldControlConfigsTest - 正常更新"() {
        given:
        def describeApiName = "TestObj"
        def fieldControlInfoHelper = Mock(FieldControlInfoHelper)
        
        def strictControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def weakControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def deleteControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        
        fieldControlInfoHelper.getStrictControl() >> strictControl
        fieldControlInfoHelper.getWeakControl() >> weakControl
        fieldControlInfoHelper.getDeleteControl() >> deleteControl
        
        strictControl.isEmpty() >> false
        weakControl.isEmpty() >> false
        deleteControl.isEmpty() >> false
        
        strictControl.getFieldNames() >> ["field1"]
        strictControl.getControlType() >> "strict"
        weakControl.getFieldNames() >> ["field2"]
        weakControl.getControlType() >> "weak"
        deleteControl.getFieldNames() >> ["field3"]
        deleteControl.getControlType() >> "delete"
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(user, describeApiName, fieldControlInfoHelper)
        
        then:
        1 * mtResourceService.modifyResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, ["field1"], MtResource.CONTROL_LEVEL_UNCHANGEABLE, MtResource.SOURCE_TYPE_FIELD_CONTROL, "strict")
        1 * mtResourceService.modifyResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, ["field2"], MtResource.CONTROL_LEVEL_UNCHANGEABLE, MtResource.SOURCE_TYPE_FIELD_CONTROL, "weak")
        1 * mtResourceService.disableResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, ["field3"], MtResource.SOURCE_TYPE_FIELD_CONTROL)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时所有控制项都为空的场景
     */
    def "updateFieldControlConfigsTest - 所有控制项都为空"() {
        given:
        def describeApiName = "TestObj"
        def fieldControlInfoHelper = Mock(FieldControlInfoHelper)
        
        def strictControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def weakControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def deleteControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        
        fieldControlInfoHelper.getStrictControl() >> strictControl
        fieldControlInfoHelper.getWeakControl() >> weakControl
        fieldControlInfoHelper.getDeleteControl() >> deleteControl
        
        strictControl.isEmpty() >> true
        weakControl.isEmpty() >> true
        deleteControl.isEmpty() >> true
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(user, describeApiName, fieldControlInfoHelper)
        
        then:
        0 * mtResourceService.modifyResource(_, _, _, _, _, _, _)
        0 * mtResourceService.disableResource(_, _, _, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时只有严格控制项不为空的场景
     */
    def "updateFieldControlConfigsTest - 只有严格控制项不为空"() {
        given:
        def describeApiName = "TestObj"
        def fieldControlInfoHelper = Mock(FieldControlInfoHelper)
        
        def strictControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def weakControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def deleteControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        
        fieldControlInfoHelper.getStrictControl() >> strictControl
        fieldControlInfoHelper.getWeakControl() >> weakControl
        fieldControlInfoHelper.getDeleteControl() >> deleteControl
        
        strictControl.isEmpty() >> false
        weakControl.isEmpty() >> true
        deleteControl.isEmpty() >> true
        
        strictControl.getFieldNames() >> ["field1", "field2"]
        strictControl.getControlType() >> "strict"
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(user, describeApiName, fieldControlInfoHelper)
        
        then:
        1 * mtResourceService.modifyResource(user.getTenantId(), describeApiName, MtResource.RESOURCE_TYPE_FILED, ["field1", "field2"], MtResource.CONTROL_LEVEL_UNCHANGEABLE, MtResource.SOURCE_TYPE_FIELD_CONTROL, "strict")
        0 * mtResourceService.disableResource(_, _, _, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时字段控制信息帮助器为null的场景
     */
    def "updateFieldControlConfigsError - 字段控制信息帮助器为null"() {
        given:
        def describeApiName = "TestObj"
        FieldControlInfoHelper fieldControlInfoHelper = null
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(user, describeApiName, fieldControlInfoHelper)
        
        then:
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时描述API名称为null的场景
     */
    def "updateFieldControlConfigsError - 描述API名称为null"() {
        given:
        String describeApiName = null
        def fieldControlInfoHelper = Mock(FieldControlInfoHelper)
        
        def strictControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def weakControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def deleteControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        
        fieldControlInfoHelper.getStrictControl() >> strictControl
        fieldControlInfoHelper.getWeakControl() >> weakControl
        fieldControlInfoHelper.getDeleteControl() >> deleteControl
        
        strictControl.isEmpty() >> false
        weakControl.isEmpty() >> true
        deleteControl.isEmpty() >> true
        
        strictControl.getFieldNames() >> ["field1"]
        strictControl.getControlType() >> "strict"
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(user, describeApiName, fieldControlInfoHelper)
        
        then:
        1 * mtResourceService.modifyResource(user.getTenantId(), null, MtResource.RESOURCE_TYPE_FILED, ["field1"], MtResource.CONTROL_LEVEL_UNCHANGEABLE, MtResource.SOURCE_TYPE_FIELD_CONTROL, "strict")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时用户为null的场景
     */
    def "updateFieldControlConfigsError - 用户为null"() {
        given:
        def describeApiName = "TestObj"
        def fieldControlInfoHelper = Mock(FieldControlInfoHelper)
        User nullUser = null
        
        def strictControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def weakControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        def deleteControl = Mock(FieldControlInfoHelper.FieldControlInfoHelperItem)
        
        fieldControlInfoHelper.getStrictControl() >> strictControl
        fieldControlInfoHelper.getWeakControl() >> weakControl
        fieldControlInfoHelper.getDeleteControl() >> deleteControl
        
        strictControl.isEmpty() >> false
        weakControl.isEmpty() >> true
        deleteControl.isEmpty() >> true
        
        strictControl.getFieldNames() >> ["field1"]
        strictControl.getControlType() >> "strict"
        
        when:
        configurationPackageResourceLogicService.updateFieldControlConfigs(nullUser, describeApiName, fieldControlInfoHelper)
        
        then:
        thrown(NullPointerException)
    }
} 