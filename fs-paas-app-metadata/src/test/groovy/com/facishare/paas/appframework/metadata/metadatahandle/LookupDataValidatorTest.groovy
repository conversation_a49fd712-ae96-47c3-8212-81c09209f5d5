package com.facishare.paas.appframework.metadata.metadatahandle

import com.facishare.crm.openapi.Utils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.appframework.metadata.ProductCategoryService
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.IObjectReferenceField
import com.facishare.paas.metadata.api.search.Wheres
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class LookupDataValidatorTest extends Specification {

    def user = Mock(User)
    def objectData = Mock(IObjectData)
    def referenceField = Mock(IObjectReferenceField)
    def productCategoryService = Mock(ProductCategoryService)
    def metaDataFindService = Mock(MetaDataFindService)
    def describeLogicService = Mock(DescribeLogicService)
    def objectDescribe = Mock(IObjectDescribe)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
        
        // 设置UdobjGrayConfig - 移除FsGrayReleaseBiz依赖
        // def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        // fsGrayReleaseBiz.isAllow(*_) >> false
    }

    def setup() {
        user.getTenantId() >> "tenant123"
        user.getUserId() >> "user123"
        referenceField.getApiName() >> "lookupField"
        referenceField.getTargetApiName() >> "TargetObj"
        referenceField.getDescribeApiName() >> "SourceObj"
        referenceField.getLabel() >> "查找字段"
        referenceField.getHelpText() >> ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的正常场景，查找数据存在
     */
    def "validateTest - 正常场景查找数据存在"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        def dataList = [Mock(IObjectData)]
        queryResult.getData() >> dataList
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找数据不存在且不抛出异常
     */
    def "validateTest - 查找数据不存在不抛出异常"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == "lookupField"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找数据不存在且抛出异常
     */
    def "validateError - 查找数据不存在抛出异常"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(true)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        describeLogicService.findObjectWithoutCopyIfGray("tenant123", "SourceObj") >> objectDescribe
        objectDescribe.getDisplayName() >> "源对象"
        
        when:
        validator.validate()
        
        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，租户在跳过验证灰度列表中
     */
    def "validateTest - 租户在跳过验证灰度列表中"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        // 模拟AppFrameworkConfig.isSkipValidateLookupGrayTenant返回true
        GroovyMock(AppFrameworkConfig, global: true)
        AppFrameworkConfig.isSkipValidateLookupGrayTenant("tenant123") >> true
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        0 * metaDataFindService.findBySearchQuery(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找字段值为null
     */
    def "validateTest - 查找字段值为null"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> null
        referenceField.getWheres() >> [Mock(Wheres)]
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        0 * metaDataFindService.findBySearchQuery(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找字段值为空字符串
     */
    def "validateTest - 查找字段值为空字符串"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> ""
        referenceField.getWheres() >> [Mock(Wheres)]
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        0 * metaDataFindService.findBySearchQuery(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，where条件为空
     */
    def "validateTest - where条件为空"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> []
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        0 * metaDataFindService.findBySearchQuery(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，where条件为null
     */
    def "validateTest - where条件为null"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> null
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        0 * metaDataFindService.findBySearchQuery(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，目标对象是产品对象
     */
    def "validateTest - 目标对象是产品对象"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        referenceField.getTargetApiName() >> Utils.PRODUCT_API_NAME
        
        def categoryList = [Mock(ProductAllCategoriesModel.CategoryPojo)]
        productCategoryService.getProductAllCategories("tenant123", "user123") >> categoryList
        productCategoryService.getCategoryChildrenCategoryCodesContainSelf(_, _) >> ["cat1", "cat2"] as Set
        
        def queryResult = Mock(QueryResult)
        def dataList = [Mock(IObjectData)]
        queryResult.getData() >> dataList
        metaDataFindService.findBySearchQuery(user, Utils.PRODUCT_API_NAME, _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == null
        1 * productCategoryService.getProductAllCategories("tenant123", "user123")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，忽略多角关系
     */
    def "validateTest - 忽略多角关系"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(true)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        def dataList = [Mock(IObjectData)]
        queryResult.getData() >> dataList
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，参考字段有帮助文本
     */
    def "validateError - 参考字段有帮助文本"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(true)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        referenceField.getHelpText() >> "这是帮助文本"
        
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        describeLogicService.findObjectWithoutCopyIfGray("tenant123", "SourceObj") >> objectDescribe
        objectDescribe.getDisplayName() >> "源对象"
        
        when:
        validator.validate()
        
        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，MetaDataFindService抛出异常
     */
    def "validateError - MetaDataFindService抛出异常"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> "data123"
        referenceField.getWheres() >> [Mock(Wheres)]
        
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> { throw new RuntimeException("Test exception") }
        
        when:
        validator.validate()
        
        then:
        thrown(RuntimeException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找字段值为数字
     */
    def "validateTest - 查找字段值为数字"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> 123
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        def dataList = [Mock(IObjectData)]
        queryResult.getData() >> dataList
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法的场景，查找字段值为布尔值
     */
    def "validateTest - 查找字段值为布尔值"() {
        given:
        def validator = LookupDataValidator.builder()
            .user(user)
            .objectData(objectData)
            .referenceField(referenceField)
            .productCategoryService(productCategoryService)
            .metaDataFindService(metaDataFindService)
            .describeLogicService(describeLogicService)
            .isIgnorePolygonal(false)
            .throwException(false)
            .build()
            
        objectData.get("lookupField") >> true
        referenceField.getWheres() >> [Mock(Wheres)]
        
        def queryResult = Mock(QueryResult)
        def dataList = [Mock(IObjectData)]
        queryResult.getData() >> dataList
        metaDataFindService.findBySearchQuery(user, "TargetObj", _) >> queryResult
        
        when:
        def result = validator.validate()
        
        then:
        result == null
    }
} 