package com.facishare.paas.appframework.metadata.mask

import com.facishare.enterprise.common.model.paas.ObjectDescribe
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import spock.lang.Specification

class MaskFieldLogicServiceImplTest extends Specification {
    MaskFieldLogicService maskFieldLogicService
    MaskFieldEncryptService maskFieldEncryptService = Mock(MaskFieldEncryptServiceImpl)

    def setup() {
        maskFieldLogicService = new MaskFieldLogicServiceImpl()
        maskFieldLogicService.maskFieldEncryptService = maskFieldEncryptService
    }

    def "test decodeMaskFieldEncryptValue"() {
        given:
        IObjectDescribe objectDescribe = Mock(ObjectDescribe) as IObjectDescribe
        when:
        def result = maskFieldLogicService.decodeMaskFieldEncryptValue(User.systemUser('74255'), [new ObjectData(['field__c__s': '13****2'])], objectDescribe)
        then:
        1 * maskFieldEncryptService.decode(*_)
    }
}
