package com.facishare.paas.appframework.metadata.metadatahandle

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.MetaDataMiscService
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class FillFieldInfoHandleImpl1Test extends Specification {

    FillFieldInfoHandleImpl fillFieldInfoHandle
    def metaDataMiscService = Mock(MetaDataMiscService)
    def user = Mock(User)
    def describe = Mock(IObjectDescribe)
    def objectData1 = Mock(IObjectData)
    def objectData2 = Mock(IObjectData)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        fillFieldInfoHandle = new FillFieldInfoHandleImpl()
        fillFieldInfoHandle.metaDataMiscService = metaDataMiscService
        
        user.getTenantId() >> "tenant123"
        describe.getApiName() >> "TestObj"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息的正常场景
     */
    def "asyncFillFieldInfoTest - 正常填充字段信息"() {
        given:
        def dataList = [objectData1, objectData2]
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 验证各个填充方法被调用
        1 * metaDataMiscService.fillObjectDataWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillUserInfo(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时数据列表为空的场景
     */
    def "asyncFillFieldInfoTest - 数据列表为空"() {
        given:
        def dataList = []
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 即使数据为空，也应该调用fillCurrencyFieldInfo和fillDataVisibilityRange
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
        // 其他方法内部有空检查，不会调用具体逻辑
        0 * metaDataMiscService.fillObjectDataWithRefObject(_, _, _)
        0 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(_, _, _)
        0 * metaDataMiscService.fillUserInfo(_, _, _)
        0 * metaDataMiscService.fillDepartmentInfo(_, _, _)
        0 * metaDataMiscService.fillCountryAreaLabel(_, _, _)
        0 * metaDataMiscService.fillDimensionFieldValue(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时数据列表为null的场景
     */
    def "asyncFillFieldInfoTest - 数据列表为null"() {
        given:
        List<IObjectData> dataList = null
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 应该调用fillCurrencyFieldInfo和fillDataVisibilityRange
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
        // 其他方法内部有空检查，不会调用具体逻辑
        0 * metaDataMiscService.fillObjectDataWithRefObject(_, _, _)
        0 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(_, _, _)
        0 * metaDataMiscService.fillUserInfo(_, _, _)
        0 * metaDataMiscService.fillDepartmentInfo(_, _, _)
        0 * metaDataMiscService.fillCountryAreaLabel(_, _, _)
        0 * metaDataMiscService.fillDimensionFieldValue(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时用户为null的场景
     */
    def "asyncFillFieldInfoTest - 用户为null"() {
        given:
        def dataList = [objectData1]
        User nullUser = null
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, nullUser)
        
        then:
        // 方法应该被调用但可能在内部处理null用户
        1 * metaDataMiscService.fillObjectDataWithRefObject(describe, _, nullUser)
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, nullUser)
        1 * metaDataMiscService.fillUserInfo(describe, _, nullUser)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, nullUser)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, nullUser)
        1 * metaDataMiscService.fillDimensionFieldValue(nullUser, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, nullUser)
        1 * metaDataMiscService.fillDataVisibilityRange(nullUser, describe, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时describe为null的场景
     */
    def "asyncFillFieldInfoTest - describe为null"() {
        given:
        def dataList = [objectData1]
        IObjectDescribe nullDescribe = null
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(nullDescribe, dataList, user)
        
        then:
        // 方法应该被调用但可能在内部处理null describe
        1 * metaDataMiscService.fillObjectDataWithRefObject(nullDescribe, _, user)
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(nullDescribe, _, user)
        1 * metaDataMiscService.fillUserInfo(nullDescribe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(nullDescribe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(nullDescribe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, nullDescribe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(nullDescribe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, nullDescribe, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时发生异常的场景
     */
    def "asyncFillFieldInfoError - 填充过程中发生异常"() {
        given:
        def dataList = [objectData1]
        
        // 模拟其中一个方法抛出异常
        metaDataMiscService.fillObjectDataWithRefObject(describe, _, user) >> { throw new RuntimeException("Test exception") }
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 其他方法仍然应该被调用（因为是并行执行）
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillUserInfo(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
        // 不应该抛出异常，因为有异常处理
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时多个方法发生异常的场景
     */
    def "asyncFillFieldInfoError - 多个方法发生异常"() {
        given:
        def dataList = [objectData1]
        
        // 模拟多个方法抛出异常
        metaDataMiscService.fillObjectDataWithRefObject(describe, _, user) >> { throw new RuntimeException("Test exception 1") }
        metaDataMiscService.fillUserInfo(describe, _, user) >> { throw new RuntimeException("Test exception 2") }
        metaDataMiscService.fillDimensionFieldValue(user, describe, _) >> { throw new RuntimeException("Test exception 3") }
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 其他方法仍然应该被调用
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
        // 不应该抛出异常，因为有异常处理
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时超时的场景
     */
    def "asyncFillFieldInfoError - 填充过程超时"() {
        given:
        def dataList = [objectData1]
        
        // 模拟长时间运行的操作
        metaDataMiscService.fillObjectDataWithRefObject(describe, _, user) >> { 
            Thread.sleep(6000) // 超过5秒超时时间
        }
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 其他方法仍然应该被调用
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillUserInfo(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
        // 不应该抛出异常，因为有异常处理
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时单个数据对象的场景
     */
    def "asyncFillFieldInfoTest - 单个数据对象"() {
        given:
        def dataList = [objectData1]
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 验证各个填充方法被调用
        1 * metaDataMiscService.fillObjectDataWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillUserInfo(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时大量数据对象的场景
     */
    def "asyncFillFieldInfoTest - 大量数据对象"() {
        given:
        def dataList = []
        for (int i = 0; i < 100; i++) {
            dataList.add(Mock(IObjectData))
        }
        
        when:
        fillFieldInfoHandle.asyncFillFieldInfo(describe, dataList, user)
        
        then:
        // 验证各个填充方法被调用
        1 * metaDataMiscService.fillObjectDataWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillObjectDataObjectManyWithRefObject(describe, _, user)
        1 * metaDataMiscService.fillUserInfo(describe, _, user)
        1 * metaDataMiscService.fillDepartmentInfo(describe, _, user)
        1 * metaDataMiscService.fillCountryAreaLabel(describe, _, user)
        1 * metaDataMiscService.fillDimensionFieldValue(user, describe, _)
        1 * metaDataMiscService.fillCurrencyFieldInfo(describe, _, user)
        1 * metaDataMiscService.fillDataVisibilityRange(user, describe, _)
    }
} 