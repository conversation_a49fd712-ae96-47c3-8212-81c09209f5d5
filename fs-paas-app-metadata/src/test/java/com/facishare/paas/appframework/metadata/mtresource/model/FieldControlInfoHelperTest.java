package com.facishare.paas.appframework.metadata.mtresource.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FieldControlInfoHelper的单元测试
 * 测试字段控制信息辅助类功能
 */
@ExtendWith(MockitoExtension.class)
class FieldControlInfoHelperTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelper构造函数的正常场景
     */
    @Test
    @DisplayName("正常场景 - FieldControlInfoHelper构造成功")
    void testFieldControlInfoHelper_Constructor() {
        // 准备测试数据
        String describeApiName = "Account";
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("STRICT");
        FieldControlInfoHelper.FieldControlInfoHelperItem weakControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("WEAK");
        FieldControlInfoHelper.FieldControlInfoHelperItem deleteControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("DELETE");

        // 执行测试
        FieldControlInfoHelper helper = new FieldControlInfoHelper(
                describeApiName, strictControl, weakControl, deleteControl);

        // 验证结果
        assertNotNull(helper);
        assertEquals(describeApiName, helper.getDescribeApiName());
        assertEquals(strictControl, helper.getStrictControl());
        assertEquals(weakControl, helper.getWeakControl());
        assertEquals(deleteControl, helper.getDeleteControl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelper构造函数时参数为null的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelper构造时参数为null")
    void testFieldControlInfoHelper_NullParameters() {
        // 执行测试
        FieldControlInfoHelper helper = new FieldControlInfoHelper(
                null, null, null, null);

        // 验证结果
        assertNotNull(helper);
        assertNull(helper.getDescribeApiName());
        assertNull(helper.getStrictControl());
        assertNull(helper.getWeakControl());
        assertNull(helper.getDeleteControl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelper构造函数时参数为空字符串的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelper构造时描述API名为空字符串")
    void testFieldControlInfoHelper_EmptyDescribeApiName() {
        // 准备测试数据
        String emptyDescribeApiName = "";
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("STRICT");

        // 执行测试
        FieldControlInfoHelper helper = new FieldControlInfoHelper(
                emptyDescribeApiName, strictControl, null, null);

        // 验证结果
        assertNotNull(helper);
        assertEquals("", helper.getDescribeApiName());
        assertEquals(strictControl, helper.getStrictControl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem创建的正常场景
     */
    @Test
    @DisplayName("正常场景 - FieldControlInfoHelperItem创建成功")
    void testFieldControlInfoHelperItem_Create() {
        // 准备测试数据
        String controlType = "STRICT";

        // 执行测试
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create(controlType);

        // 验证结果
        assertNotNull(item);
        assertEquals(controlType, item.getControlType());
        assertNotNull(item.getFieldNames());
        assertTrue(item.getFieldNames().isEmpty());
        assertTrue(item.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem创建时控制类型为null的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelperItem创建时控制类型为null")
    void testFieldControlInfoHelperItem_CreateWithNullControlType() {
        // 执行测试
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create(null);

        // 验证结果
        assertNotNull(item);
        assertNull(item.getControlType());
        assertNotNull(item.getFieldNames());
        assertTrue(item.getFieldNames().isEmpty());
        assertTrue(item.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem创建时控制类型为空字符串的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelperItem创建时控制类型为空字符串")
    void testFieldControlInfoHelperItem_CreateWithEmptyControlType() {
        // 准备测试数据
        String emptyControlType = "";

        // 执行测试
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create(emptyControlType);

        // 验证结果
        assertNotNull(item);
        assertEquals("", item.getControlType());
        assertNotNull(item.getFieldNames());
        assertTrue(item.getFieldNames().isEmpty());
        assertTrue(item.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem的isEmpty方法
     */
    @Test
    @DisplayName("功能验证 - FieldControlInfoHelperItem的isEmpty方法")
    void testFieldControlInfoHelperItem_IsEmpty() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 验证初始状态为空
        assertTrue(item.isEmpty());

        // 添加字段名后验证不为空
        item.appendFieldName("field1");
        assertFalse(item.isEmpty());

        // 验证字段名列表
        assertEquals(1, item.getFieldNames().size());
        assertEquals("field1", item.getFieldNames().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem的appendFieldName方法
     */
    @Test
    @DisplayName("正常场景 - FieldControlInfoHelperItem添加字段名成功")
    void testFieldControlInfoHelperItem_AppendFieldName() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 执行测试 - 添加多个字段名
        item.appendFieldName("field1");
        item.appendFieldName("field2");
        item.appendFieldName("field3");

        // 验证结果
        assertFalse(item.isEmpty());
        assertEquals(3, item.getFieldNames().size());
        assertEquals("field1", item.getFieldNames().get(0));
        assertEquals("field2", item.getFieldNames().get(1));
        assertEquals("field3", item.getFieldNames().get(2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem添加null字段名的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelperItem添加null字段名")
    void testFieldControlInfoHelperItem_AppendNullFieldName() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 执行测试
        item.appendFieldName(null);

        // 验证结果
        assertFalse(item.isEmpty());
        assertEquals(1, item.getFieldNames().size());
        assertNull(item.getFieldNames().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem添加空字符串字段名的场景
     */
    @Test
    @DisplayName("边界场景 - FieldControlInfoHelperItem添加空字符串字段名")
    void testFieldControlInfoHelperItem_AppendEmptyFieldName() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 执行测试
        item.appendFieldName("");

        // 验证结果
        assertFalse(item.isEmpty());
        assertEquals(1, item.getFieldNames().size());
        assertEquals("", item.getFieldNames().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem添加重复字段名的场景
     */
    @Test
    @DisplayName("正常场景 - FieldControlInfoHelperItem添加重复字段名")
    void testFieldControlInfoHelperItem_AppendDuplicateFieldName() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 执行测试 - 添加重复字段名
        item.appendFieldName("field1");
        item.appendFieldName("field1");
        item.appendFieldName("field2");
        item.appendFieldName("field1");

        // 验证结果 - 应该允许重复
        assertFalse(item.isEmpty());
        assertEquals(4, item.getFieldNames().size());
        assertEquals("field1", item.getFieldNames().get(0));
        assertEquals("field1", item.getFieldNames().get(1));
        assertEquals("field2", item.getFieldNames().get(2));
        assertEquals("field1", item.getFieldNames().get(3));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldControlInfoHelperItem的字段名列表可变性
     */
    @Test
    @DisplayName("功能验证 - FieldControlInfoHelperItem字段名列表可变性")
    void testFieldControlInfoHelperItem_FieldNamesMutability() {
        // 准备测试数据
        FieldControlInfoHelper.FieldControlInfoHelperItem item = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("TEST");

        // 获取字段名列表引用
        List<String> fieldNames = item.getFieldNames();

        // 直接修改列表
        fieldNames.add("directAdd");

        // 验证修改生效
        assertFalse(item.isEmpty());
        assertEquals(1, item.getFieldNames().size());
        assertEquals("directAdd", item.getFieldNames().get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试完整的使用场景
     */
    @Test
    @DisplayName("集成场景 - 完整的字段控制信息使用场景")
    void testFieldControlInfoHelper_CompleteUsageScenario() {
        // 准备测试数据
        String describeApiName = "Account";
        
        // 创建严格控制项
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("STRICT");
        strictControl.appendFieldName("name");
        strictControl.appendFieldName("phone");
        
        // 创建弱控制项
        FieldControlInfoHelper.FieldControlInfoHelperItem weakControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("WEAK");
        weakControl.appendFieldName("description");
        
        // 创建删除控制项（空）
        FieldControlInfoHelper.FieldControlInfoHelperItem deleteControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("DELETE");

        // 创建主对象
        FieldControlInfoHelper helper = new FieldControlInfoHelper(
                describeApiName, strictControl, weakControl, deleteControl);

        // 验证完整结果
        assertEquals("Account", helper.getDescribeApiName());
        
        // 验证严格控制
        assertFalse(helper.getStrictControl().isEmpty());
        assertEquals("STRICT", helper.getStrictControl().getControlType());
        assertEquals(2, helper.getStrictControl().getFieldNames().size());
        assertTrue(helper.getStrictControl().getFieldNames().contains("name"));
        assertTrue(helper.getStrictControl().getFieldNames().contains("phone"));
        
        // 验证弱控制
        assertFalse(helper.getWeakControl().isEmpty());
        assertEquals("WEAK", helper.getWeakControl().getControlType());
        assertEquals(1, helper.getWeakControl().getFieldNames().size());
        assertTrue(helper.getWeakControl().getFieldNames().contains("description"));
        
        // 验证删除控制
        assertTrue(helper.getDeleteControl().isEmpty());
        assertEquals("DELETE", helper.getDeleteControl().getControlType());
        assertEquals(0, helper.getDeleteControl().getFieldNames().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象的不可变性（Lombok生成的final字段）
     */
    @Test
    @DisplayName("不可变性验证 - 验证final字段的不可变性")
    void testFieldControlInfoHelper_Immutability() {
        // 准备测试数据
        String describeApiName = "TestObject";
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("STRICT");

        // 创建对象
        FieldControlInfoHelper helper = new FieldControlInfoHelper(
                describeApiName, strictControl, null, null);

        // 验证字段值不变
        assertEquals("TestObject", helper.getDescribeApiName());
        assertEquals(strictControl, helper.getStrictControl());
        
        // 验证引用一致性
        assertSame(strictControl, helper.getStrictControl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法（Lombok生成）
     */
    @Test
    @DisplayName("Lombok验证 - 验证equals和hashCode方法")
    void testFieldControlInfoHelper_EqualsAndHashCode() {
        // 准备测试数据
        String describeApiName = "TestObject";
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = 
                FieldControlInfoHelper.FieldControlInfoHelperItem.create("STRICT");

        // 创建两个相同的对象
        FieldControlInfoHelper helper1 = new FieldControlInfoHelper(
                describeApiName, strictControl, null, null);
        FieldControlInfoHelper helper2 = new FieldControlInfoHelper(
                describeApiName, strictControl, null, null);

        // 验证equals
        assertEquals(helper1, helper2);
        assertEquals(helper1.hashCode(), helper2.hashCode());
        
        // 验证toString不为null
        assertNotNull(helper1.toString());
        assertTrue(helper1.toString().contains("TestObject"));
    }
}
