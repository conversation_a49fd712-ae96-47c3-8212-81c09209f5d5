package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.google.common.reflect.TypeToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Arrays;import java.util.stream.Stream;
import java.util.stream.Collectors;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskDelegateInitSceneProvider的单元测试
 * 测试任务委托场景初始化提供者的功能
 */
@ExtendWith(MockitoExtension.class)
class TaskDelegateInitSceneProviderTest {

    @InjectMocks
    private TaskDelegateInitSceneProvider taskDelegateInitSceneProvider;

    private User testUser;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User("74255", "1000");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法返回正确的API名称
     */
    @Test
    @DisplayName("正常场景 - getApiName返回正确的API名称")
    void testGetApiName_ReturnsCorrectApiName() {
        // 执行测试
        String result = taskDelegateInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(Utils.STAGE_TASK_API_NAME, result);
        assertEquals("StageTaskObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList返回正确的搜索模板列表")
    void testGetDefaultSearchTemplateList_ReturnsCorrectTemplates() {
        // 准备测试数据
        String apiName = "TaskDelegateLogObj";
        String extendAttribute = null;

        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证模板的API名称
        List<String> apiNames = result.stream().map(ISearchTemplate::getApiName).collect(Collectors.toList());
        assertTrue(apiNames.contains("in_progress"));
        assertTrue(apiNames.contains("pass"));
        assertTrue(apiNames.contains("all"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法验证模板详细信息
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList验证模板详细信息")
    void testGetDefaultSearchTemplateList_ValidatesTemplateDetails() {
        // 准备测试数据
        String apiName = "TaskDelegateLogObj";

        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证待处理模板
        ISearchTemplate inProgressTemplate = result.stream()
            .filter(template -> "in_progress".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(inProgressTemplate);
        assertTrue(inProgressTemplate.getLabel().contains("待处理"));
        assertFalse(inProgressTemplate.getIsDefault());

        // 验证已处理模板
        ISearchTemplate passTemplate = result.stream()
            .filter(template -> "pass".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(passTemplate);
        assertTrue(passTemplate.getLabel().contains("已处理"));
        assertFalse(passTemplate.getIsDefault());

        // 验证全部模板
        ISearchTemplate allTemplate = result.stream()
            .filter(template -> "all".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(allTemplate);
        assertTrue(allTemplate.getLabel().contains("全部"));
        assertTrue(allTemplate.getIsDefault());

        // 验证每个模板都有过滤器
        for (ISearchTemplate template : result) {
            assertNotNull(template.getFilters());
            assertFalse(template.getFilters().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理不同用户的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理不同租户用户")
    void testGetDefaultSearchTemplateList_DifferentTenantUser() {
        // 准备测试数据 - 不同租户的用户
        User differentTenantUser = new User("78057", "2000");
        String apiName = "TaskDelegateLogObj";

        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(differentTenantUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证模板的租户ID正确设置
        for (ISearchTemplate template : result) {
            assertNotNull(template);
            assertEquals("78057", template.getTenantId());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的过滤器JSON解析
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList正确解析过滤器JSON")
    void testGetDefaultSearchTemplateList_ParsesFiltersCorrectly() {
        // 准备测试数据
        String apiName = "TaskDelegateLogObj";

        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证每个模板都有过滤器且类型正确
        Type filterType = new TypeToken<List<IFilter>>() {}.getType();
        for (ISearchTemplate template : result) {
            assertNotNull(template.getFilters());
            assertFalse(template.getFilters().isEmpty());
            assertTrue(template.getFilters() instanceof List);
            
            // 验证过滤器包含必要的字段
            @SuppressWarnings("unchecked")
            List<IFilter> filters = (List<IFilter>) template.getFilters();
            boolean hasObjectDescribeFilter = filters.stream()
                .anyMatch(filter -> "object_describe_api_name".equals(filter.getFieldName()));
            assertTrue(hasObjectDescribeFilter, "Should contain object_describe_api_name filter");
            
            boolean hasDelegatePersonFilter = filters.stream()
                .anyMatch(filter -> "delegate_person_id".equals(filter.getFieldName()));
            assertTrue(hasDelegatePersonFilter, "Should contain delegate_person_id filter");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法验证特定过滤器逻辑
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList验证特定过滤器逻辑")
    void testGetDefaultSearchTemplateList_ValidatesSpecificFilterLogic() {
        // 准备测试数据
        String apiName = "TaskDelegateLogObj";

        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证待处理模板的状态过滤器
        ISearchTemplate inProgressTemplate = result.stream()
            .filter(template -> "in_progress".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(inProgressTemplate);
        
        @SuppressWarnings("unchecked")
        List<IFilter> inProgressFilters = (List<IFilter>) inProgressTemplate.getFilters();
        boolean hasInProgressStateFilter = inProgressFilters.stream()
            .anyMatch(filter -> "state".equals(filter.getFieldName()) 
                && filter.getFieldValues().contains("in_progress"));
        assertTrue(hasInProgressStateFilter, "In progress template should filter by in_progress state");

        // 验证已处理模板的状态过滤器
        ISearchTemplate passTemplate = result.stream()
            .filter(template -> "pass".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(passTemplate);
        
        @SuppressWarnings("unchecked")
        List<IFilter> passFilters = (List<IFilter>) passTemplate.getFilters();
        boolean hasPassStateFilter = passFilters.stream()
            .anyMatch(filter -> "state".equals(filter.getFieldName()) 
                && filter.getFieldValues().contains("pass"));
        assertTrue(hasPassStateFilter, "Pass template should filter by pass state");

        // 验证全部模板的状态过滤器
        ISearchTemplate allTemplate = result.stream()
            .filter(template -> "all".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(allTemplate);
        
        @SuppressWarnings("unchecked")
        List<IFilter> allFilters = (List<IFilter>) allTemplate.getFilters();
        boolean hasAllStateFilter = allFilters.stream()
            .anyMatch(filter -> "state".equals(filter.getFieldName()) 
                && filter.getFieldValues().contains("pass") 
                && filter.getFieldValues().contains("in_progress"));
        assertTrue(hasAllStateFilter, "All template should filter by both pass and in_progress states");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideLabelTestData")
    @DisplayName("参数化测试 - getLabel返回正确的标签")
    void testGetLabel_ReturnsCorrectLabels(String apiName, String expectedLabelKeyword) {
        // 执行测试
        String result = taskDelegateInitSceneProvider.getLabel(apiName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(expectedLabelKeyword), 
            String.format("Expected label to contain '%s' but was '%s'", expectedLabelKeyword, result));
    }

    /**
     * 提供getLabel测试的参数化数据
     */
    private static Stream<Arguments> provideLabelTestData() {
        return Stream.of(
            Arguments.of("in_progress", "待处理"),
            Arguments.of("pass", "已处理"),
            Arguments.of("all", "全部")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法处理未知API名称的场景
     */
    @Test
    @DisplayName("边界场景 - getLabel处理未知API名称返回null")
    void testGetLabel_UnknownApiName_ReturnsNull() {
        // 执行测试
        String result = taskDelegateInitSceneProvider.getLabel("unknown_api_name");

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法处理null参数的场景
     */
    @Test
    @DisplayName("边界场景 - getLabel处理null参数返回null")
    void testGetLabel_NullApiName_ReturnsNull() {
        // 执行测试
        String result = taskDelegateInitSceneProvider.getLabel(null);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null用户的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理null用户抛出异常")
    void testGetDefaultSearchTemplateListThrowsNullPointerException_NullUser() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            taskDelegateInitSceneProvider.getDefaultSearchTemplateList(null, "TaskDelegateLogObj", null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null API名称的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理null API名称")
    void testGetDefaultSearchTemplateList_NullApiName() {
        // 执行测试
        List<ISearchTemplate> result = taskDelegateInitSceneProvider.getDefaultSearchTemplateList(testUser, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证模板的API名称设置
        for (ISearchTemplate template : result) {
            assertNotNull(template.getApiName());
            assertTrue(Arrays.asList("in_progress", "pass", "all").contains(template.getApiName()));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(taskDelegateInitSceneProvider);
    }
}
