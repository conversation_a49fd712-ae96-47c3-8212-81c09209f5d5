package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.describe.Formula;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * CalculatorFactory的单元测试
 * 测试计算器工厂的功能
 */
@ExtendWith(MockitoExtension.class)
class CalculatorFactoryTest {

    @Mock
    private MetaDataFindService mockMetaDataFindService;

    @Mock
    private ExpressionService mockExpressionService;

    @Mock
    private FieldDescribeExt mockFieldDescribeExt;

    private IObjectDescribe testObjectDescribe;
    private IFieldDescribe testFieldDescribe;
    private Formula testFormulaField;

    @BeforeEach
    void setUp() {
        // 构造测试对象描述
        testObjectDescribe = new ObjectDescribe();
        testObjectDescribe.setApiName("TestObject");
        testObjectDescribe.setTenantId("74255");
        
        // 构造测试字段描述
        Map<String, Object> fieldConfig = Maps.newHashMap();
        fieldConfig.put("api_name", "test_field");
        fieldConfig.put("type", "TEXT");
        testFieldDescribe = FieldDescribeFactory.newInstance(fieldConfig);
        
        // 构造测试公式字段
        testFormulaField = mock(Formula.class);
        when(testFormulaField.getApiName()).thenReturn("formula_field");
        when(testFormulaField.getType()).thenReturn("FORMULA");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建公式字段计算器
     */
    @Test
    @DisplayName("创建字段计算器 - 公式字段")
    void testCreateFieldCalculator_公式字段() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFormulaField))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(true);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            // 执行被测试方法
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFormulaField, false);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof FormulaFieldCalculator);
            
            // 验证Mock交互
            verify(mockFieldDescribeExt).isFormula();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建计算值字段计算器
     */
    @Test
    @DisplayName("创建字段计算器 - 计算值字段")
    void testCreateFieldCalculator_计算值字段() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(true);
            
            // 执行被测试方法
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof CalculateValueCalculator);
            
            // 验证Mock交互
            verify(mockFieldDescribeExt).isFormula();
            verify(mockFieldDescribeExt).hasCalculateValue();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建默认值字段计算器
     */
    @Test
    @DisplayName("创建字段计算器 - 默认值字段")
    void testCreateFieldCalculator_默认值字段() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            // 执行被测试方法
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof DefaultValueCalculator);
            
            // 验证Mock交互
            verify(mockFieldDescribeExt).isFormula();
            verify(mockFieldDescribeExt).hasCalculateValue();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器时忽略无效变量
     */
    @Test
    @DisplayName("创建字段计算器 - 忽略无效变量")
    void testCreateFieldCalculator_忽略无效变量() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFormulaField))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(true);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            // 执行被测试方法 - 设置ignoreInvalidVariable为true
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFormulaField, true);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof FormulaFieldCalculator);
            
            // 验证Mock交互
            verify(mockFieldDescribeExt).isFormula();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器时传入null参数
     */
    @Test
    @DisplayName("创建字段计算器 - 传入null参数")
    void testCreateFieldCalculator_传入null参数() {
        // 测试传入null的MetaDataFindService
        assertThrows(Exception.class, () -> {
            CalculatorFactory.createFieldCalculator(null, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
        });
        
        // 测试传入null的ExpressionService
        assertThrows(Exception.class, () -> {
            CalculatorFactory.createFieldCalculator(mockMetaDataFindService, null, testObjectDescribe, testFieldDescribe, false);
        });
        
        // 测试传入null的ObjectDescribe
        assertThrows(Exception.class, () -> {
            CalculatorFactory.createFieldCalculator(mockMetaDataFindService, mockExpressionService, null, testFieldDescribe, false);
        });
        
        // 测试传入null的FieldDescribe
        assertThrows(Exception.class, () -> {
            CalculatorFactory.createFieldCalculator(mockMetaDataFindService, mockExpressionService, testObjectDescribe, null, false);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器时FieldDescribeExt抛出异常
     */
    @Test
    @DisplayName("创建字段计算器 - FieldDescribeExt抛出异常")
    void testCreateFieldCalculator_FieldDescribeExt抛出异常() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为 - 抛出异常
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenThrow(new RuntimeException("FieldDescribeExt异常"));
            
            // 执行被测试方法并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                CalculatorFactory.createFieldCalculator(
                        mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            });
            
            // 验证异常信息
            assertEquals("FieldDescribeExt异常", exception.getMessage());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器的优先级判断
     */
    @Test
    @DisplayName("创建字段计算器 - 优先级判断")
    void testCreateFieldCalculator_优先级判断() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为 - 既是公式字段又有计算值
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(true);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(true);
            
            // 执行被测试方法
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            // 验证结果 - 公式字段优先级更高
            assertNotNull(result);
            assertTrue(result instanceof FormulaFieldCalculator);
            
            // 验证Mock交互 - 只检查isFormula，不检查hasCalculateValue
            verify(mockFieldDescribeExt).isFormula();
            verify(mockFieldDescribeExt, never()).hasCalculateValue();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器时计算器初始化
     */
    @Test
    @DisplayName("创建字段计算器 - 计算器初始化")
    void testCreateFieldCalculator_计算器初始化() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            // 执行被测试方法
            AbstractFieldCalculator result = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            // 验证结果 - 计算器应该已经初始化
            assertNotNull(result);
            assertTrue(result instanceof DefaultValueCalculator);
            
            // 注意：由于init()方法是在具体实现类中，我们无法直接验证是否调用了init()
            // 但是可以验证计算器对象已经创建成功
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建字段计算器的所有分支覆盖
     */
    @Test
    @DisplayName("创建字段计算器 - 所有分支覆盖")
    void testCreateFieldCalculator_所有分支覆盖() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 测试分支1：公式字段
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(any(IFieldDescribe.class)))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(true);
            
            AbstractFieldCalculator formulaCalculator = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFormulaField, false);
            assertTrue(formulaCalculator instanceof FormulaFieldCalculator);
            
            // 重置Mock
            reset(mockFieldDescribeExt);
            
            // 测试分支2：计算值字段
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(true);
            
            AbstractFieldCalculator calculateCalculator = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            assertTrue(calculateCalculator instanceof CalculateValueCalculator);
            
            // 重置Mock
            reset(mockFieldDescribeExt);
            
            // 测试分支3：默认值字段
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            AbstractFieldCalculator defaultCalculator = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            assertTrue(defaultCalculator instanceof DefaultValueCalculator);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试工厂方法的静态特性
     */
    @Test
    @DisplayName("工厂方法 - 静态特性")
    void testFactoryMethod_静态特性() {
        // 验证工厂方法是静态的，可以直接调用
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(testFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isFormula()).thenReturn(false);
            when(mockFieldDescribeExt.hasCalculateValue()).thenReturn(false);
            
            // 直接调用静态方法
            AbstractFieldCalculator result1 = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            AbstractFieldCalculator result2 = CalculatorFactory.createFieldCalculator(
                    mockMetaDataFindService, mockExpressionService, testObjectDescribe, testFieldDescribe, false);
            
            // 验证每次调用都创建新的实例
            assertNotNull(result1);
            assertNotNull(result2);
            assertNotSame(result1, result2); // 不是同一个实例
            assertEquals(result1.getClass(), result2.getClass()); // 但是同一个类型
        }
    }
}
