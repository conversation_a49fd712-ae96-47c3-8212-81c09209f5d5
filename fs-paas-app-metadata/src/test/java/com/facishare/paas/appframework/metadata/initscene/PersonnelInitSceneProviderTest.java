package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PersonnelInitSceneProvider的单元测试
 * 测试人员初始化场景提供器的功能
 */
@ExtendWith(MockitoExtension.class)
class PersonnelInitSceneProviderTest {

    @Mock
    private ISearchTemplateService mockSearchTemplateService;

    @Mock
    private User mockUser;

    @InjectMocks
    private PersonnelInitSceneProvider personnelInitSceneProvider;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法
     */
    @Test
    @DisplayName("正常场景 - getApiName返回人员对象API名称")
    void testGetApiName_ReturnsPersonnelApiName() {
        // 执行测试
        String result = personnelInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(ObjectDescribeExt.PERSONNEL_OBJ_API_NAME, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList获取人员默认搜索模板")
    void testGetDefaultSearchTemplateList_Success() {
        // 准备测试数据
        String apiName = "Personnel";
        String extendAttribute = "testAttribute";

        // 执行测试
        List<ISearchTemplate> result = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(8, result.size()); // 应该有8个预定义的搜索模板

        // 验证模板的API名称
        boolean hasAllEmployee = result.stream().anyMatch(template -> "all_employee".equals(template.getApiName()));
        boolean hasNotActive = result.stream().anyMatch(template -> "not_active".equals(template.getApiName()));
        boolean hasPauseLogin = result.stream().anyMatch(template -> "pause_login".equals(template.getApiName()));
        boolean hasLeaderNull = result.stream().anyMatch(template -> "leader_null".equals(template.getApiName()));
        boolean hasInThisLevelDepartment = result.stream().anyMatch(template -> "in_this_level_department".equals(template.getApiName()));
        boolean hasInMainDepartment = result.stream().anyMatch(template -> "in_main_department".equals(template.getApiName()));
        boolean hasStopEmployee = result.stream().anyMatch(template -> "stop_employee".equals(template.getApiName()));
        boolean hasMainDepartmentNull = result.stream().anyMatch(template -> "main_department_null".equals(template.getApiName()));

        assertTrue(hasAllEmployee, "应该包含在职员工模板");
        assertTrue(hasNotActive, "应该包含未激活模板");
        assertTrue(hasPauseLogin, "应该包含禁止登录模板");
        assertTrue(hasLeaderNull, "应该包含无汇报对象模板");
        assertTrue(hasInThisLevelDepartment, "应该包含仅看本级部门模板");
        assertTrue(hasInMainDepartment, "应该包含仅看主属模板");
        assertTrue(hasStopEmployee, "应该包含已停用模板");
        assertTrue(hasMainDepartmentNull, "应该包含未分配主属部门模板");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList时传入null参数
     */
    @Test
    @DisplayName("边界场景 - getDefaultSearchTemplateList传入null参数")
    void testGetDefaultSearchTemplateList_NullParameters() {
        // 测试传入null用户
        List<ISearchTemplate> result1 = personnelInitSceneProvider.getDefaultSearchTemplateList(null, "Personnel", "attr");
        assertNotNull(result1);
        assertEquals(8, result1.size()); // 即使用户为null，也应该返回模板

        // 测试传入null apiName
        List<ISearchTemplate> result2 = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, null, "attr");
        assertNotNull(result2);
        assertEquals(8, result2.size());

        // 测试传入null extendAttribute
        List<ISearchTemplate> result3 = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, "Personnel", null);
        assertNotNull(result3);
        assertEquals(8, result3.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - getLabel获取标签名称")
    void testGetLabel_Success() {
        // 测试各种API名称的标签
        assertEquals("在职员工", personnelInitSceneProvider.getLabel("all_employee"));
        assertEquals("未激活的", personnelInitSceneProvider.getLabel("not_active"));
        assertEquals("禁止登录", personnelInitSceneProvider.getLabel("pause_login"));
        assertEquals("无汇报对象", personnelInitSceneProvider.getLabel("leader_null"));
        assertEquals("仅看本级部门", personnelInitSceneProvider.getLabel("in_this_level_department"));
        assertEquals("仅看主属", personnelInitSceneProvider.getLabel("in_main_department"));
        assertEquals("已停用", personnelInitSceneProvider.getLabel("stop_employee"));
        assertEquals("未分配主属部门", personnelInitSceneProvider.getLabel("main_department_null"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel时传入未知API名称
     */
    @Test
    @DisplayName("边界场景 - getLabel传入未知API名称")
    void testGetLabel_UnknownApiName() {
        // 测试传入未知的API名称
        String result = personnelInitSceneProvider.getLabel("unknown_api_name");
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel时传入null参数
     */
    @Test
    @DisplayName("边界场景 - getLabel传入null参数")
    void testGetLabel_NullParameter() {
        // 测试传入null参数
        String result = personnelInitSceneProvider.getLabel(null);
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel时传入空字符串
     */
    @Test
    @DisplayName("边界场景 - getLabel传入空字符串")
    void testGetLabel_EmptyString() {
        // 测试传入空字符串
        String result = personnelInitSceneProvider.getLabel("");
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的复杂场景
     */
    @Test
    @DisplayName("复杂场景 - getDefaultSearchTemplateList验证模板内容")
    void testGetDefaultSearchTemplateList_VerifyTemplateContent() {
        // 准备测试数据
        String apiName = "Personnel";
        String extendAttribute = "testAttribute";

        // 执行测试
        List<ISearchTemplate> result = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(8, result.size());

//        // 验证每个模板都有正确的属性
//        for (ISearchTemplate template : result) {
//            assertNotNull(template.getApiName(), "模板API名称不应为null");
//            assertNotNull(template.getName(), "模板名称不应为null");
//
//            // 验证特定模板的内容
//            if ("all_employee".equals(template.getApiName())) {
//                assertEquals("在职员工", template.getName());
//            } else if ("not_active".equals(template.getApiName())) {
//                assertEquals("未激活的", template.getName());
//            }
//        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList的性能
     */
    @Test
    @DisplayName("性能场景 - getDefaultSearchTemplateList执行性能")
    void testGetDefaultSearchTemplateList_Performance() {
        // 准备测试数据
        String apiName = "Personnel";
        String extendAttribute = "testAttribute";

        // 测试执行时间
        long startTime = System.currentTimeMillis();
        
        List<ISearchTemplate> result = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证结果和性能
        assertNotNull(result);
        assertEquals(8, result.size());
        assertTrue(executionTime < 500, "方法执行时间过长: " + executionTime + "ms");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用的一致性
     */
    @Test
    @DisplayName("一致性测试 - 多次调用getDefaultSearchTemplateList结果一致")
    void testGetDefaultSearchTemplateList_Consistency() {
        // 准备测试数据
        String apiName = "Personnel";
        String extendAttribute = "testAttribute";

        // 多次调用
        List<ISearchTemplate> result1 = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);
        List<ISearchTemplate> result2 = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);
        List<ISearchTemplate> result3 = personnelInitSceneProvider.getDefaultSearchTemplateList(mockUser, apiName, extendAttribute);

        // 验证结果一致性
        assertEquals(result1.size(), result2.size());
        assertEquals(result2.size(), result3.size());
        
        // 验证API名称一致性
        for (int i = 0; i < result1.size(); i++) {
            assertEquals(result1.get(i).getApiName(), result2.get(i).getApiName());
            assertEquals(result2.get(i).getApiName(), result3.get(i).getApiName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法的完整性
     */
    @Test
    @DisplayName("完整性测试 - getLabel覆盖所有预定义的API名称")
    void testGetLabel_Completeness() {
        // 定义所有预期的API名称
        String[] expectedApiNames = {
            "all_employee", "not_active", "pause_login", "leader_null",
            "in_this_level_department", "in_main_department", "stop_employee", "main_department_null"
        };

        // 验证每个API名称都有对应的标签
        for (String apiName : expectedApiNames) {
            String label = personnelInitSceneProvider.getLabel(apiName);
            assertNotNull(label, "API名称 " + apiName + " 应该有对应的标签");
            assertFalse(label.isEmpty(), "API名称 " + apiName + " 的标签不应为空");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(personnelInitSceneProvider);
        assertNotNull(mockSearchTemplateService);
        assertNotNull(mockUser);
    }
}
