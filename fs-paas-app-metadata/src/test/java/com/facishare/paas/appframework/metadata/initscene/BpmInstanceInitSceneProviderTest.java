package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.IFilter;import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.github.autoconf.ConfigFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;import java.util.stream.Stream;
import java.util.stream.Collectors;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BpmInstanceInitSceneProvider的单元测试
 * 测试BPM实例场景初始化提供者的功能
 */
@ExtendWith(MockitoExtension.class)
class BpmInstanceInitSceneProviderTest {

    @Mock
    private ISearchTemplateService searchTemplateService;

    @InjectMocks
    private BpmInstanceInitSceneProvider bpmInstanceInitSceneProvider;

    private User testUser;
    private User grayTenantUser;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User("74255", "1000");
        grayTenantUser = new User("78057", "2000");
        
        // 注入searchTemplateService到父类
        ReflectionTestUtils.setField(bpmInstanceInitSceneProvider, "searchTemplateService", searchTemplateService);
        
        // 初始化tenantIds列表为空（非灰度状态）
        List<String> emptyTenantIds = new ArrayList<>();
        ReflectionTestUtils.setField(bpmInstanceInitSceneProvider, "tenantIds", emptyTenantIds);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法返回正确的API名称
     */
    @Test
    @DisplayName("正常场景 - getApiName返回正确的API名称")
    void testGetApiName_ReturnsCorrectApiName() {
        // 执行测试
        String result = bpmInstanceInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(Utils.BPM_INSTANCE_API_NAME, result);
        assertEquals("BpmInstance", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法的正常场景 - 配置存在且有灰度租户ID
     */
    @Test
    @DisplayName("正常场景 - init方法正确解析灰度租户ID配置")
    void testInit_WithGrayTenantIdConfig() {
        try (MockedStatic<ConfigFactory> mockedConfigFactory = mockStatic(ConfigFactory.class)) {
            // 准备测试数据
            String grayTenantIdConfig = "74255,78057,79999";
            


            // 执行测试
            bpmInstanceInitSceneProvider.init();

            // 验证结果
            @SuppressWarnings("unchecked")
            List<String> tenantIds = (List<String>) ReflectionTestUtils.getField(bpmInstanceInitSceneProvider, "tenantIds");
            assertNotNull(tenantIds);
            assertEquals(3, tenantIds.size());
            assertTrue(tenantIds.contains("74255"));
            assertTrue(tenantIds.contains("78057"));
            assertTrue(tenantIds.contains("79999"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法处理空配置的场景
     */
    @Test
    @DisplayName("边界场景 - init方法处理空配置清空租户列表")
    void testInit_WithEmptyConfig() {
        try (MockedStatic<ConfigFactory> mockedConfigFactory = mockStatic(ConfigFactory.class)) {
            // 配置Mock行为 - 返回空配置


            // 执行测试
            bpmInstanceInitSceneProvider.init();

            // 验证结果
            @SuppressWarnings("unchecked")
            List<String> tenantIds = (List<String>) ReflectionTestUtils.getField(bpmInstanceInitSceneProvider, "tenantIds");
            assertNotNull(tenantIds);
            assertTrue(tenantIds.isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的正常场景（非灰度企业）
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList非灰度企业返回自定义模板")
    void testGetDefaultSearchTemplateList_NonGrayTenant_ReturnsCustomTemplates() {
        // 准备测试数据
        String apiName = "BpmInstance";
        
        // 执行测试
        List<ISearchTemplate> result = bpmInstanceInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证模板的API名称
        List<String> apiNames = result.stream().map(ISearchTemplate::getApiName).collect(Collectors.toList());
        assertTrue(apiNames.contains("in_progress"));
        assertTrue(apiNames.contains("pass"));
        assertTrue(apiNames.contains("error"));
        assertTrue(apiNames.contains("cancel"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的灰度企业场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList灰度企业调用父类方法")
    void testGetDefaultSearchTemplateList_GrayTenant_CallsSuperMethod() throws MetadataServiceException {
        // 准备测试数据 - 设置为灰度企业
        List<String> grayTenantIds = new ArrayList<>();
        grayTenantIds.add("78057");
        ReflectionTestUtils.setField(bpmInstanceInitSceneProvider, "tenantIds", grayTenantIds);
        
        List<ISearchTemplate> mockTemplates = new ArrayList<>();
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenReturn(mockTemplates);

        // 执行测试
        List<ISearchTemplate> result = bpmInstanceInitSceneProvider.getDefaultSearchTemplateList(grayTenantUser, "BpmInstance", null);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTemplates, result);
        
        // 验证调用了父类的searchTemplateService
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("78057"), eq("BpmInstance"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideLabelTestData")
    @DisplayName("参数化测试 - getLabel返回正确的标签")
    void testGetLabel_ReturnsCorrectLabels(String apiName, String expectedLabelKeyword) {
        // 执行测试
        String result = bpmInstanceInitSceneProvider.getLabel(apiName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(expectedLabelKeyword) || result.length() > 0, 
            String.format("Expected label to contain '%s' but was '%s'", expectedLabelKeyword, result));
    }

    /**
     * 提供getLabel测试的参数化数据
     */
    private static Stream<Arguments> provideLabelTestData() {
        return Stream.of(
            Arguments.of("in_progress", "进行"),
            Arguments.of("pass", "完成"),
            Arguments.of("error", "异常"),
            Arguments.of("cancel", "终止")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabel方法处理未知API名称的场景
     */
    @Test
    @DisplayName("边界场景 - getLabel处理未知API名称返回null")
    void testGetLabel_UnknownApiName_ReturnsNull() {
        // 执行测试
        String result = bpmInstanceInitSceneProvider.getLabel("unknown_api_name");

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null用户的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理null用户抛出异常")
    void testGetDefaultSearchTemplateListThrowsNullPointerException_NullUser() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            bpmInstanceInitSceneProvider.getDefaultSearchTemplateList(null, "BpmInstance", null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(bpmInstanceInitSceneProvider);
    }
}
