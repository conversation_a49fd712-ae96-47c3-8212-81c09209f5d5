package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FileExtUtil工具类的单元测试
 * 测试文件扩展名解析和图片字段处理功能
 */
@ExtendWith(MockitoExtension.class)
class FileExtUtilTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试解析文件扩展名的正常场景
     */
    @Test
    @DisplayName("正常场景 - 解析文件扩展名成功")
    void testParseFileExt_Success() {
        // 测试常见文件类型
        assertEquals("txt", FileExtUtil.parseFileExt("document.txt"));
        assertEquals("pdf", FileExtUtil.parseFileExt("report.pdf"));
        assertEquals("jpg", FileExtUtil.parseFileExt("image.jpg"));
        assertEquals("png", FileExtUtil.parseFileExt("photo.png"));
        assertEquals("docx", FileExtUtil.parseFileExt("file.docx"));
        assertEquals("xlsx", FileExtUtil.parseFileExt("spreadsheet.xlsx"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析文件扩展名时，文件名为空的场景
     */
    @Test
    @DisplayName("边界场景 - 解析文件扩展名时文件名为空返回null")
    void testParseFileExt_EmptyFileName() {
        assertNull(FileExtUtil.parseFileExt(null));
        assertNull(FileExtUtil.parseFileExt(""));
        assertNull(FileExtUtil.parseFileExt("   "));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析文件扩展名时，文件名没有扩展名的场景
     */
    @Test
    @DisplayName("边界场景 - 解析文件扩展名时文件名没有扩展名返回null")
    void testParseFileExt_NoExtension() {
        assertNull(FileExtUtil.parseFileExt("filename"));
        assertNull(FileExtUtil.parseFileExt("document"));
        assertNull(FileExtUtil.parseFileExt("test"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析文件扩展名时，文件名只有点号的场景
     */
    @Test
    @DisplayName("边界场景 - 解析文件扩展名时文件名只有点号返回null")
    void testParseFileExt_OnlyDot() {
        assertNull(FileExtUtil.parseFileExt("."));
        assertNull(FileExtUtil.parseFileExt("filename."));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析文件扩展名时，文件名有多个点号的场景
     */
    @Test
    @DisplayName("边界场景 - 解析文件扩展名时文件名有多个点号取第一个扩展名")
    void testParseFileExt_MultipleDots() {
        // 注意：当前实现只取第一个点后的内容，这可能不是期望的行为
        assertEquals("backup", FileExtUtil.parseFileExt("file.backup.txt"));
        assertEquals("min", FileExtUtil.parseFileExt("script.min.js"));
        assertEquals("tar", FileExtUtil.parseFileExt("archive.tar.gz"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否为嵌入式图片字段的正常场景
     */
    @Test
    @DisplayName("正常场景 - 检查嵌入式图片字段成功")
    void testIsEmbeddedImageField_Success() {
        // 准备测试数据
        IFieldDescribe imageField = mock(IFieldDescribe.class);
        when(imageField.getType()).thenReturn(IFieldType.IMAGE);
        when(imageField.getApiName()).thenReturn("avatar" + FileExtUtil.IMAGE_FLAG + "field");

        // 执行测试
        boolean result = FileExtUtil.isEmbeddedImageField(imageField);

        // 验证结果
        assertTrue(result);
        verify(imageField).getType();
        verify(imageField).getApiName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否为嵌入式图片字段时，字段类型不是图片的场景
     */
    @Test
    @DisplayName("正常场景 - 检查嵌入式图片字段时字段类型不是图片返回false")
    void testIsEmbeddedImageField_NotImageType() {
        // 准备测试数据
        IFieldDescribe textField = mock(IFieldDescribe.class);
        when(textField.getType()).thenReturn(IFieldType.TEXT);

        // 执行测试
        boolean result = FileExtUtil.isEmbeddedImageField(textField);

        // 验证结果
        assertFalse(result);
        verify(textField).getType();
        verify(textField, never()).getApiName(); // 不应该调用getApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否为嵌入式图片字段时，字段名不包含图片标识的场景
     */
    @Test
    @DisplayName("正常场景 - 检查嵌入式图片字段时字段名不包含图片标识返回false")
    void testIsEmbeddedImageField_NoImageFlag() {
        // 准备测试数据
        IFieldDescribe imageField = mock(IFieldDescribe.class);
        when(imageField.getType()).thenReturn(IFieldType.IMAGE);
        when(imageField.getApiName()).thenReturn("normalImageField");

        // 执行测试
        boolean result = FileExtUtil.isEmbeddedImageField(imageField);

        // 验证结果
        assertFalse(result);
        verify(imageField).getType();
        verify(imageField).getApiName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否为嵌入式图片字段时，字段名为空的场景
     */
    @Test
    @DisplayName("边界场景 - 检查嵌入式图片字段时字段名为空返回false")
    void testIsEmbeddedImageField_EmptyApiName() {
        // 准备测试数据
        IFieldDescribe imageField = mock(IFieldDescribe.class);
        when(imageField.getType()).thenReturn(IFieldType.IMAGE);
        when(imageField.getApiName()).thenReturn("");

        // 执行测试
        boolean result = FileExtUtil.isEmbeddedImageField(imageField);

        // 验证结果
        assertFalse(result);
        verify(imageField).getType();
        verify(imageField).getApiName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否为嵌入式图片字段时，字段名为null的场景
     */
    @Test
    @DisplayName("边界场景 - 检查嵌入式图片字段时字段名为null返回false")
    void testIsEmbeddedImageField_NullApiName() {
        // 准备测试数据
        IFieldDescribe imageField = mock(IFieldDescribe.class);
        when(imageField.getType()).thenReturn(IFieldType.IMAGE);
        when(imageField.getApiName()).thenReturn(null);

        // 执行测试
        boolean result = FileExtUtil.isEmbeddedImageField(imageField);

        // 验证结果
        assertFalse(result);
        verify(imageField).getType();
        verify(imageField).getApiName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提取原始字段名的正常场景
     */
    @Test
    @DisplayName("正常场景 - 提取原始字段名成功")
    void testExtractImageOriginalFieldName_Success() {
        // 测试包含图片标识的字段名
        String fieldNameWithFlag = "avatar" + FileExtUtil.IMAGE_FLAG + "field";
        String result = FileExtUtil.extractImageOriginalFieldName(fieldNameWithFlag);
        assertEquals("avatar", result);

        // 测试其他包含图片标识的字段名
        String anotherFieldName = "profile" + FileExtUtil.IMAGE_FLAG + "image";
        String anotherResult = FileExtUtil.extractImageOriginalFieldName(anotherFieldName);
        assertEquals("profile", anotherResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提取原始字段名时，字段名不包含图片标识的场景
     */
    @Test
    @DisplayName("正常场景 - 提取原始字段名时字段名不包含图片标识返回原字段名")
    void testExtractImageOriginalFieldName_NoImageFlag() {
        // 测试不包含图片标识的字段名
        String normalFieldName = "normalField";
        String result = FileExtUtil.extractImageOriginalFieldName(normalFieldName);
        assertEquals("normalField", result);

        // 测试空字符串
        String emptyFieldName = "";
        String emptyResult = FileExtUtil.extractImageOriginalFieldName(emptyFieldName);
        assertEquals("", emptyResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提取原始字段名时，图片标识在字段名开头的场景
     */
    @Test
    @DisplayName("边界场景 - 提取原始字段名时图片标识在字段名开头返回空字符串")
    void testExtractImageOriginalFieldName_ImageFlagAtStart() {
        // 测试图片标识在开头的情况
        String fieldNameStartWithFlag = FileExtUtil.IMAGE_FLAG + "field";
        String result = FileExtUtil.extractImageOriginalFieldName(fieldNameStartWithFlag);
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提取原始字段名时，字段名只有图片标识的场景
     */
    @Test
    @DisplayName("边界场景 - 提取原始字段名时字段名只有图片标识返回空字符串")
    void testExtractImageOriginalFieldName_OnlyImageFlag() {
        // 测试只有图片标识的情况
        String onlyFlag = FileExtUtil.IMAGE_FLAG;
        String result = FileExtUtil.extractImageOriginalFieldName(onlyFlag);
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提取原始字段名时，字段名包含多个图片标识的场景
     */
    @Test
    @DisplayName("边界场景 - 提取原始字段名时字段名包含多个图片标识取第一个")
    void testExtractImageOriginalFieldName_MultipleImageFlags() {
        // 测试包含多个图片标识的情况
        String multipleFlags = "field" + FileExtUtil.IMAGE_FLAG + "middle" + FileExtUtil.IMAGE_FLAG + "end";
        String result = FileExtUtil.extractImageOriginalFieldName(multipleFlags);
        assertEquals("field", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证图片标识常量的值
     */
    @Test
    @DisplayName("常量验证 - 验证图片标识常量值")
    void testImageFlagConstant() {
        assertEquals("@@image@@", FileExtUtil.IMAGE_FLAG);
        assertNotNull(FileExtUtil.IMAGE_FLAG);
        assertFalse(FileExtUtil.IMAGE_FLAG.isEmpty());
    }
}
