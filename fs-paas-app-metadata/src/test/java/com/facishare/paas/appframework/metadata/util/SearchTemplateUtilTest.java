package com.facishare.paas.appframework.metadata.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Operator;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SearchTemplateUtil工具类的单元测试 测试搜索模板构建和过滤器创建功能
 */
@ExtendWith(MockitoExtension.class)
class SearchTemplateUtilTest {

    /**
     * GenerateByAI 测试内容描述：测试创建过滤器的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建过滤器成功")
    void testGetFilterByPara_Success() {
        // 准备测试数据
        Operator operator = Operator.EQ;
        String fieldName = "name";
        List<String> values = Arrays.asList("value1", "value2");
        boolean objectReference = false;

        // 执行测试
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // 验证结果
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals("AND", result.getConnector());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
        assertEquals(objectReference, result.isObjectReference());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建过滤器时使用对象引用的场景
     */
    @Test
    @DisplayName("正常场景 - 创建过滤器时使用对象引用")
    void testGetFilterByPara_WithObjectReference() {
        // 准备测试数据
        Operator operator = Operator.IN;
        String fieldName = "accountId";
        List<String> values = Arrays.asList("acc1", "acc2", "acc3");
        boolean objectReference = true;

        // 执行测试
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // 验证结果
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
        assertTrue(result.isObjectReference());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建过滤器时使用空值列表的场景
     */
    @Test
    @DisplayName("边界场景 - 创建过滤器时使用空值列表")
    void testGetFilterByPara_EmptyValueList() {
        // 准备测试数据
        Operator operator = Operator.NEQ;
        String fieldName = "status";
        List<String> values = Collections.emptyList();
        boolean objectReference = false;

        // 执行测试
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // 验证结果
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
        assertTrue(result.getFieldValues().isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建过滤器时使用null值列表的场景
     */
    @Test
    @DisplayName("边界场景 - 创建过滤器时使用null值列表")
    void testGetFilterByPara_NullValueList() {
        // 准备测试数据
        Operator operator = Operator.LIKE;
        String fieldName = "description";
        List<String> values = null;
        boolean objectReference = false;

        // 执行测试
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // 验证结果
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals(fieldName, result.getFieldName());
        assertNull(result.getFieldValues());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建搜索模板的简化版本（不包含字段列表）
     */
    @Test
    @DisplayName("正常场景 - 创建搜索模板简化版本成功")
    void testGetSearchTemplate_SimpleVersion_Success() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Account";
        String label = "默认搜索模板";
        boolean isDefault = true;
        List<IFilter> filters = Arrays.asList(
                SearchTemplateUtil.getFilterByPara(Operator.EQ, "status", Arrays.asList("active"), false)
        );

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // 验证结果
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertEquals(isDefault, result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertFalse(result.getIsHidden());
        assertEquals("CRM", result.getPackage());
        assertEquals("default", result.getType());
        assertEquals("system", result.getCreatedBy());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建搜索模板的完整版本（包含字段列表）
     */
    @Test
    @DisplayName("正常场景 - 创建搜索模板完整版本成功")
    void testGetSearchTemplate_FullVersion_Success() {
        // 准备测试数据
        String tenantId = "tenant456";
        String apiName = "Contact";
        String label = "联系人搜索模板";
        boolean isDefault = false;
        List<IFilter> filters = Arrays.asList(
                SearchTemplateUtil.getFilterByPara(Operator.NEQ, "status", Arrays.asList("deleted"), false)
        );
        List<String> fieldList = Arrays.asList("name", "email", "phone");
        Integer fieldListType = 1;

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters, fieldList, fieldListType);

        // 验证结果
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertEquals(isDefault, result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertEquals(fieldListType, result.getFieldListType());

        // 验证字段列表不为空
        assertNotNull(result.getFieldList());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建搜索模板时字段列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - 创建搜索模板时字段列表为空")
    void testGetSearchTemplate_EmptyFieldList() {
        // 准备测试数据
        String tenantId = "tenant789";
        String apiName = "Opportunity";
        String label = "机会搜索模板";
        boolean isDefault = true;
        List<IFilter> filters = Collections.emptyList();
        List<String> fieldList = Collections.emptyList();
        Integer fieldListType = 2;

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters, fieldList, fieldListType);

        // 验证结果
        assertNotNull(result);
        assertEquals(fieldListType, result.getFieldListType());
        // 空的字段列表不应该设置到搜索模板中
        assertNull(result.getFieldList());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建搜索模板时字段列表为null的场景
     */
    @Test
    @DisplayName("边界场景 - 创建搜索模板时字段列表为null")
    void testGetSearchTemplate_NullFieldList() {
        // 准备测试数据
        String tenantId = "tenant000";
        String apiName = "Lead";
        String label = "线索搜索模板";
        boolean isDefault = false;
        List<IFilter> filters = null;
        List<String> fieldList = null;
        Integer fieldListType = null;

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters, fieldList, fieldListType);

        // 验证结果
        assertNotNull(result);
        // 当filters为null时，实际返回空列表
        assertTrue(result.getFilters().isEmpty());
        assertNull(result.getFieldList());
        assertNull(result.getFieldListType());
    }

    /**
     * GenerateByAI 测试内容描述：测试创建搜索模板时使用空字符串参数的场景
     */
    @Test
    @DisplayName("边界场景 - 创建搜索模板时使用空字符串参数")
    void testGetSearchTemplate_EmptyStringParameters() {
        // 准备测试数据
        String tenantId = "";
        String apiName = "";
        String label = "";
        boolean isDefault = true;
        List<IFilter> filters = Collections.emptyList();

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getTenantId());
        assertEquals("", result.getObjectDescribeApiName());
        assertEquals("", result.getLabel());
        assertTrue(result.getIsDefault());
    }

    /**
     * GenerateByAI 测试内容描述：验证搜索模板的默认值设置
     */
    @Test
    @DisplayName("默认值验证 - 验证搜索模板的默认值设置")
    void testGetSearchTemplate_DefaultValues() {
        // 准备测试数据
        String tenantId = "test";
        String apiName = "Test";
        String label = "测试";
        boolean isDefault = false;
        List<IFilter> filters = Collections.emptyList();

        // 执行测试
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // 验证默认值
        assertFalse(result.getIsHidden()); // 默认不隐藏
        assertEquals("CRM", result.getPackage()); // 默认包名
        assertEquals("default", result.getType()); // 默认类型
        assertEquals("system", result.getCreatedBy()); // 默认创建者
    }

    /**
     * GenerateByAI 测试内容描述：验证需要添加默认搜索模板的API名称常量
     */
    @Test
    @DisplayName("常量验证 - 验证需要添加默认搜索模板的API名称常量")
    void testNeedAddDefaultSearchTemplateApiNames() {
        // 验证常量不为空
        assertNotNull(SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES);

        // 验证常量包含预期的API名称
        assertTrue(SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES.contains(Utils.CASES_API_NAME));

        // 验证常量是不可修改的
        assertThrows(UnsupportedOperationException.class, () -> {
            SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES.add("newApiName");
        });
    }

    /**
     * GenerateByAI 测试内容描述：测试多种操作符的过滤器创建
     */
    @Test
    @DisplayName("操作符验证 - 测试多种操作符的过滤器创建")
    void testGetFilterByPara_DifferentOperators() {
        String fieldName = "testField";
        List<String> values = Arrays.asList("test");

        // 测试不同的操作符
        IFilter equalsFilter = SearchTemplateUtil.getFilterByPara(Operator.EQ, fieldName, values, false);
        assertEquals(Operator.EQ, equalsFilter.getOperator());

        IFilter notEqualsFilter = SearchTemplateUtil.getFilterByPara(Operator.NEQ, fieldName, values, false);
        assertEquals(Operator.NEQ, notEqualsFilter.getOperator());

        IFilter inFilter = SearchTemplateUtil.getFilterByPara(Operator.IN, fieldName, values, false);
        assertEquals(Operator.IN, inFilter.getOperator());

        IFilter likeFilter = SearchTemplateUtil.getFilterByPara(Operator.LIKE, fieldName, values, false);
        assertEquals(Operator.LIKE, likeFilter.getOperator());

        // 验证所有过滤器都有相同的基本属性
        IFilter[] filters = {equalsFilter, notEqualsFilter, inFilter, likeFilter};
        for (IFilter filter : filters) {
            assertEquals("AND", filter.getConnector());
            assertEquals(fieldName, filter.getFieldName());
            assertEquals(values, filter.getFieldValues());
            assertFalse(filter.isObjectReference());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试方法返回值的不可变性
     */
    @Test
    @DisplayName("不可变性验证 - 验证相同输入产生相同输出")
    void testMethodImmutability() {
        // 测试过滤器创建的一致性
        Operator operator = Operator.EQ;
        String fieldName = "name";
        List<String> values = Arrays.asList("value1");
        boolean objectReference = false;

        IFilter filter1 = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);
        IFilter filter2 = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // 验证两个过滤器具有相同的属性
        assertEquals(filter1.getOperator(), filter2.getOperator());
        assertEquals(filter1.getFieldName(), filter2.getFieldName());
        assertEquals(filter1.getFieldValues(), filter2.getFieldValues());
        assertEquals(filter1.isObjectReference(), filter2.isObjectReference());

        // 测试搜索模板创建的一致性
        String tenantId = "tenant";
        String apiName = "API";
        String label = "Label";
        boolean isDefault = true;
        List<IFilter> filters = Arrays.asList(filter1);

        ISearchTemplate template1 = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);
        ISearchTemplate template2 = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // 验证两个搜索模板具有相同的属性
        assertEquals(template1.getTenantId(), template2.getTenantId());
        assertEquals(template1.getObjectDescribeApiName(), template2.getObjectDescribeApiName());
        assertEquals(template1.getLabel(), template2.getLabel());
        assertEquals(template1.getIsDefault(), template2.getIsDefault());
    }
}
