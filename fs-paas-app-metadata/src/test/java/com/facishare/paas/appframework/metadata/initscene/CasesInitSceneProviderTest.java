package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CasesInitSceneProvider的单元测试
 * 测试工单场景初始化提供者的功能
 */
@ExtendWith(MockitoExtension.class)
class CasesInitSceneProviderTest {

    @Mock
    private ISearchTemplateService searchTemplateService;

    @InjectMocks
    private CasesInitSceneProvider casesInitSceneProvider;

    private User testUser;
    private List<ISearchTemplate> mockParentTemplates;

    @BeforeEach
    void setUp() throws MetadataServiceException {
        // 创建测试用户
        testUser = new User("74255", "1000");
        
        // 注入searchTemplateService到父类
        ReflectionTestUtils.setField(casesInitSceneProvider, "searchTemplateService", searchTemplateService);
        
        // 准备父类方法返回的模板列表
        mockParentTemplates = new ArrayList<>();
        ISearchTemplate mockTemplate = mock(ISearchTemplate.class);
        when(mockTemplate.getTenantId()).thenReturn("74255");
        when(mockTemplate.getApiName()).thenReturn("ALL");
        mockParentTemplates.add(mockTemplate);
        
        // 配置父类方法的Mock行为
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenReturn(mockParentTemplates);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法返回正确的API名称
     */
    @Test
    @DisplayName("正常场景 - getApiName返回正确的API名称")
    void testGetApiName_ReturnsCorrectApiName() {
        // 执行测试
        String result = casesInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(Utils.CASES_API_NAME, result);
        assertEquals("CasesObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList返回正确的搜索模板列表")
    void testGetDefaultSearchTemplateList_ReturnsCorrectTemplates() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "CasesObj";
        String extendAttribute = null;

        // 执行测试
        List<ISearchTemplate> result = casesInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size()); // 1个父类模板 + 3个自定义模板

        // 验证父类方法被调用
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), eq("CasesObj"), any());

        // 验证新增的模板API名称
        List<String> newTemplateApiNames = result.stream()
            .map(ISearchTemplate::getApiName)
            .filter(apiName1 -> !apiName1.equals("ALL"))
            .collect(Collectors.toList());
        
        assertTrue(newTemplateApiNames.contains("NotSolveCases"));
        assertTrue(newTemplateApiNames.contains("PriorityEmergencyCases"));
        assertTrue(newTemplateApiNames.contains("CloseCases"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法验证模板详细信息
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList验证模板详细信息")
    void testGetDefaultSearchTemplateList_ValidatesTemplateDetails() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "CasesObj";

        // 执行测试
        List<ISearchTemplate> result = casesInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证未解决工单模板
        ISearchTemplate notSolveTemplate = result.stream()
            .filter(template -> "NotSolveCases".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(notSolveTemplate);
        assertTrue(notSolveTemplate.getLabel().contains("未解决") || notSolveTemplate.getLabel().contains("工单"));
        assertNotNull(notSolveTemplate.getFilters());
        assertFalse(notSolveTemplate.getFilters().isEmpty());

        // 验证紧急工单模板
        ISearchTemplate urgentTemplate = result.stream()
            .filter(template -> "PriorityEmergencyCases".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(urgentTemplate);
        assertTrue(urgentTemplate.getLabel().contains("紧急") || urgentTemplate.getLabel().contains("工单"));

        // 验证关闭工单模板
        ISearchTemplate closeTemplate = result.stream()
            .filter(template -> "CloseCases".equals(template.getApiName()))
            .findFirst()
            .orElse(null);
        assertNotNull(closeTemplate);
        assertTrue(closeTemplate.getLabel().contains("关闭") || closeTemplate.getLabel().contains("工单"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理不同用户的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理不同租户用户")
    void testGetDefaultSearchTemplateList_DifferentTenantUser() throws MetadataServiceException {
        // 准备测试数据 - 不同租户的用户
        User differentTenantUser = new User("78057", "2000");
        String apiName = "CasesObj";

        // 配置Mock返回不同租户的模板
        List<ISearchTemplate> mockDifferentTenantTemplates = new ArrayList<>();
        ISearchTemplate mockTemplate = mock(ISearchTemplate.class);
        when(mockTemplate.getTenantId()).thenReturn("78057");
        when(mockTemplate.getApiName()).thenReturn("ALL");
        mockDifferentTenantTemplates.add(mockTemplate);
        
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(eq("78057"), anyString(), any()))
            .thenReturn(mockDifferentTenantTemplates);

        // 执行测试
        List<ISearchTemplate> result = casesInitSceneProvider.getDefaultSearchTemplateList(differentTenantUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());
        
        // 验证调用了正确租户的服务
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("78057"), eq("CasesObj"), any());
        
        // 验证新增模板的租户ID正确设置
        List<ISearchTemplate> newTemplates = result.stream()
            .filter(template -> !template.getApiName().equals("ALL"))
            .collect(Collectors.toList());
        
        for (ISearchTemplate template : newTemplates) {
            assertEquals("78057", template.getTenantId());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法验证过滤器逻辑
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList验证过滤器逻辑")
    void testGetDefaultSearchTemplateList_ValidatesFilterLogic() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "CasesObj";

        // 执行测试
        List<ISearchTemplate> result = casesInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        
        // 验证每个新增模板都有基础过滤器和权限过滤器
        List<ISearchTemplate> newTemplates = result.stream()
            .filter(template -> !template.getApiName().equals("ALL"))
            .collect(Collectors.toList());
        
        for (ISearchTemplate template : newTemplates) {
            assertNotNull(template.getFilters());
            @SuppressWarnings("unchecked")
            List<IFilter> filters = (List<IFilter>) template.getFilters();
            assertFalse(filters.isEmpty());
            
            // 验证包含基础过滤器（object_describe_api_name）
            boolean hasBaseFilter = filters.stream()
                .anyMatch(filter -> "object_describe_api_name".equals(filter.getFieldName()) 
                    && Operator.EQ.equals(filter.getOperator()));
            assertTrue(hasBaseFilter, "Should contain base object_describe_api_name filter");
            
            // 验证包含权限过滤器（relevant_team.teamMemberEmployee）
            boolean hasPermissionFilter = filters.stream()
                .anyMatch(filter -> "relevant_team.teamMemberEmployee".equals(filter.getFieldName()) 
                    && Operator.IN.equals(filter.getOperator()));
            assertTrue(hasPermissionFilter, "Should contain permission filter");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理父类异常的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理父类异常仍返回自定义模板")
    void testGetDefaultSearchTemplateList_HandlesParentException() throws MetadataServiceException {
        // 配置Mock抛出异常
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenThrow(new RuntimeException("Service error"));

        // 执行测试
        List<ISearchTemplate> result = casesInitSceneProvider.getDefaultSearchTemplateList(testUser, "CasesObj", null);

        // 验证结果 - 即使父类方法异常，仍应返回自定义模板
        assertNotNull(result);
        assertEquals(3, result.size()); // 只有3个自定义模板，没有父类模板
        
        // 验证自定义模板的API名称
        List<String> apiNames = result.stream().map(ISearchTemplate::getApiName).collect(Collectors.toList());
        assertTrue(apiNames.contains("NotSolveCases"));
        assertTrue(apiNames.contains("PriorityEmergencyCases"));
        assertTrue(apiNames.contains("CloseCases"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null用户的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理null用户抛出异常")
    void testGetDefaultSearchTemplateListThrowsNullPointerException_NullUser() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            casesInitSceneProvider.getDefaultSearchTemplateList(null, "CasesObj", null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(casesInitSceneProvider);
    }
}
