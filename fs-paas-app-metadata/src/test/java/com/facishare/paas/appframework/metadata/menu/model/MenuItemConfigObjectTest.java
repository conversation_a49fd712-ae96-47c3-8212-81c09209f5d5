package com.facishare.paas.appframework.metadata.menu.model;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MenuItemConfigObject的单元测试
 * 测试菜单项配置对象的功能
 */
@ExtendWith(MockitoExtension.class)
class MenuItemConfigObjectTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试MenuItemConfigObject无参构造函数
     */
    @Test
    @DisplayName("正常场景 - 无参构造函数创建成功")
    void testMenuItemConfigObject_NoArgsConstructor() {
        // 执行测试
        MenuItemConfigObject configObject = new MenuItemConfigObject();

        // 验证结果
        assertNotNull(configObject);
        assertNull(configObject.getApiName());
        assertNull(configObject.getDisplayName());
        assertNull(configObject.getNumber());
        assertNull(configObject.getIconIndex());
        assertNull(configObject.getIconPathHome());
        assertNull(configObject.getIconPathMenu());
        assertNull(configObject.getDeviceType());
        assertNull(configObject.getItemType());
        assertNull(configObject.getValidatePrivilege());
        assertNull(configObject.getValidateDescribe());
        assertNull(configObject.getUrl());
        assertNull(configObject.getMobileConfig());
        assertNull(configObject.getUseDefaultUrl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MenuItemConfigObject有参构造函数
     */
    @Test
    @DisplayName("正常场景 - 有参构造函数创建成功")
    void testMenuItemConfigObject_ArgsConstructor() {
        // 准备测试数据
        Boolean validatePrivilege = true;
        Boolean validateDescribe = false;
        String deviceType = "web";

        // 执行测试
        MenuItemConfigObject configObject = new MenuItemConfigObject(
                validatePrivilege, validateDescribe, deviceType);

        // 验证结果
        assertNotNull(configObject);
        assertEquals(validatePrivilege, configObject.getValidatePrivilege());
        assertEquals(validateDescribe, configObject.getValidateDescribe());
        assertEquals(deviceType, configObject.getDeviceType());
        
        // 验证其他字段为null
        assertNull(configObject.getApiName());
        assertNull(configObject.getDisplayName());
        assertNull(configObject.getNumber());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MenuItemConfigObject有参构造函数时参数为null
     */
    @Test
    @DisplayName("边界场景 - 有参构造函数参数为null")
    void testMenuItemConfigObject_ArgsConstructorWithNullValues() {
        // 执行测试
        MenuItemConfigObject configObject = new MenuItemConfigObject(null, null, null);

        // 验证结果
        assertNotNull(configObject);
        assertNull(configObject.getValidatePrivilege());
        assertNull(configObject.getValidateDescribe());
        assertNull(configObject.getDeviceType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MenuItemConfigObject的setter和getter方法
     */
    @Test
    @DisplayName("功能验证 - setter和getter方法")
    void testMenuItemConfigObject_SettersAndGetters() {
        // 准备测试数据
        MenuItemConfigObject configObject = new MenuItemConfigObject();
        
        String apiName = "Account";
        String displayName = "客户";
        Integer number = 1;
        Integer iconIndex = 10;
        String iconPathHome = "/icons/home/<USER>";
        String iconPathMenu = "/icons/menu/account.png";
        String deviceType = "all";
        String itemType = "predef_obj";
        Boolean validatePrivilege = true;
        Boolean validateDescribe = false;
        String url = "/account/list";
        Boolean useDefaultUrl = true;

        // 执行测试 - 设置所有字段
        configObject.setApiName(apiName);
        configObject.setDisplayName(displayName);
        configObject.setNumber(number);
        configObject.setIconIndex(iconIndex);
        configObject.setIconPathHome(iconPathHome);
        configObject.setIconPathMenu(iconPathMenu);
        configObject.setDeviceType(deviceType);
        configObject.setItemType(itemType);
        configObject.setValidatePrivilege(validatePrivilege);
        configObject.setValidateDescribe(validateDescribe);
        configObject.setUrl(url);
        configObject.setUseDefaultUrl(useDefaultUrl);

        // 验证结果
        assertEquals(apiName, configObject.getApiName());
        assertEquals(displayName, configObject.getDisplayName());
        assertEquals(number, configObject.getNumber());
        assertEquals(iconIndex, configObject.getIconIndex());
        assertEquals(iconPathHome, configObject.getIconPathHome());
        assertEquals(iconPathMenu, configObject.getIconPathMenu());
        assertEquals(deviceType, configObject.getDeviceType());
        assertEquals(itemType, configObject.getItemType());
        assertEquals(validatePrivilege, configObject.getValidatePrivilege());
        assertEquals(validateDescribe, configObject.getValidateDescribe());
        assertEquals(url, configObject.getUrl());
        assertEquals(useDefaultUrl, configObject.getUseDefaultUrl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MobileConfig内部类的创建和使用
     */
    @Test
    @DisplayName("正常场景 - MobileConfig内部类功能")
    void testMobileConfig_Creation() {
        // 准备测试数据
        MenuItemConfigObject.MobileConfig mobileConfig = new MenuItemConfigObject.MobileConfig();
        
        String mobileAddAction = "add_account";
        String mobileListAction = "list_account";

        // 执行测试
        mobileConfig.setMobileAddAction(mobileAddAction);
        mobileConfig.setMobileListAction(mobileListAction);

        // 验证结果
        assertEquals(mobileAddAction, mobileConfig.getMobileAddAction());
        assertEquals(mobileListAction, mobileConfig.getMobileListAction());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MenuItemConfigObject与MobileConfig的关联
     */
    @Test
    @DisplayName("正常场景 - MenuItemConfigObject与MobileConfig关联")
    void testMenuItemConfigObject_WithMobileConfig() {
        // 准备测试数据
        MenuItemConfigObject configObject = new MenuItemConfigObject();
        MenuItemConfigObject.MobileConfig mobileConfig = new MenuItemConfigObject.MobileConfig();
        
        mobileConfig.setMobileAddAction("mobile_add");
        mobileConfig.setMobileListAction("mobile_list");

        // 执行测试
        configObject.setMobileConfig(mobileConfig);

        // 验证结果
        assertNotNull(configObject.getMobileConfig());
        assertEquals("mobile_add", configObject.getMobileConfig().getMobileAddAction());
        assertEquals("mobile_list", configObject.getMobileConfig().getMobileListAction());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化功能
     */
    @Test
    @DisplayName("JSON序列化 - 对象转JSON字符串")
    void testMenuItemConfigObject_JsonSerialization() {
        // 准备测试数据
        MenuItemConfigObject configObject = new MenuItemConfigObject();
        configObject.setApiName("Account");
        configObject.setDisplayName("客户");
        configObject.setNumber(1);
        configObject.setDeviceType("all");
        configObject.setValidatePrivilege(true);
        configObject.setValidateDescribe(false);

        // 执行测试
        String jsonString = JSON.toJSONString(configObject);

        // 验证结果
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("\"api_name\":\"Account\""));
        assertTrue(jsonString.contains("\"display_name\":\"客户\""));
        assertTrue(jsonString.contains("\"number\":1"));
        assertTrue(jsonString.contains("\"device_type\":\"all\""));
        assertTrue(jsonString.contains("\"validate_privilege\":true"));
        assertTrue(jsonString.contains("\"validate_describe\":false"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON反序列化功能
     */
    @Test
    @DisplayName("JSON反序列化 - JSON字符串转对象")
    void testMenuItemConfigObject_JsonDeserialization() {
        // 准备测试数据
        String jsonString = "{" +
                "\"api_name\":\"Contact\"," +
                "\"display_name\":\"联系人\"," +
                "\"number\":2," +
                "\"icon_index\":20," +
                "\"device_type\":\"web\"," +
                "\"item_type\":\"predef_obj\"," +
                "\"validate_privilege\":false," +
                "\"validate_describe\":true," +
                "\"url\":\"/contact/list\"," +
                "\"useDefaultUrl\":false" +
                "}";

        // 执行测试
        MenuItemConfigObject configObject = JSON.parseObject(jsonString, MenuItemConfigObject.class);

        // 验证结果
        assertNotNull(configObject);
        assertEquals("Contact", configObject.getApiName());
        assertEquals("联系人", configObject.getDisplayName());
        assertEquals(Integer.valueOf(2), configObject.getNumber());
        assertEquals(Integer.valueOf(20), configObject.getIconIndex());
        assertEquals("web", configObject.getDeviceType());
        assertEquals("predef_obj", configObject.getItemType());
        assertEquals(false, configObject.getValidatePrivilege());
        assertEquals(true, configObject.getValidateDescribe());
        assertEquals("/contact/list", configObject.getUrl());
        assertEquals(false, configObject.getUseDefaultUrl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试包含MobileConfig的JSON序列化
     */
    @Test
    @DisplayName("JSON序列化 - 包含MobileConfig的完整序列化")
    void testMenuItemConfigObject_JsonSerializationWithMobileConfig() {
        // 准备测试数据
        MenuItemConfigObject configObject = new MenuItemConfigObject();
        configObject.setApiName("Opportunity");
        configObject.setDisplayName("商机");
        
        MenuItemConfigObject.MobileConfig mobileConfig = new MenuItemConfigObject.MobileConfig();
        mobileConfig.setMobileAddAction("add_opportunity");
        mobileConfig.setMobileListAction("list_opportunity");
        configObject.setMobileConfig(mobileConfig);

        // 执行测试
        String jsonString = JSON.toJSONString(configObject);

        // 验证结果
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("\"api_name\":\"Opportunity\""));
        assertTrue(jsonString.contains("\"display_name\":\"商机\""));
        assertTrue(jsonString.contains("\"mobile_config\""));
        assertTrue(jsonString.contains("\"mobile_add_action\":\"add_opportunity\""));
        assertTrue(jsonString.contains("\"mobile_list_action\":\"list_opportunity\""));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试包含MobileConfig的JSON反序列化
     */
    @Test
    @DisplayName("JSON反序列化 - 包含MobileConfig的完整反序列化")
    void testMenuItemConfigObject_JsonDeserializationWithMobileConfig() {
        // 准备测试数据
        String jsonString = "{" +
                "\"api_name\":\"Lead\"," +
                "\"display_name\":\"线索\"," +
                "\"mobile_config\":{" +
                "\"mobile_add_action\":\"add_lead\"," +
                "\"mobile_list_action\":\"list_lead\"" +
                "}" +
                "}";

        // 执行测试
        MenuItemConfigObject configObject = JSON.parseObject(jsonString, MenuItemConfigObject.class);

        // 验证结果
        assertNotNull(configObject);
        assertEquals("Lead", configObject.getApiName());
        assertEquals("线索", configObject.getDisplayName());
        assertNotNull(configObject.getMobileConfig());
        assertEquals("add_lead", configObject.getMobileConfig().getMobileAddAction());
        assertEquals("list_lead", configObject.getMobileConfig().getMobileListAction());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界值的处理
     */
    @Test
    @DisplayName("边界场景 - 各种边界值的处理")
    void testMenuItemConfigObject_BoundaryValues() {
        // 准备测试数据
        MenuItemConfigObject configObject = new MenuItemConfigObject();
        
        // 设置边界值
        configObject.setApiName("");
        configObject.setDisplayName("");
        configObject.setNumber(0);
        configObject.setIconIndex(-1);
        configObject.setDeviceType("");
        configObject.setUrl("");

        // 验证结果
        assertEquals("", configObject.getApiName());
        assertEquals("", configObject.getDisplayName());
        assertEquals(Integer.valueOf(0), configObject.getNumber());
        assertEquals(Integer.valueOf(-1), configObject.getIconIndex());
        assertEquals("", configObject.getDeviceType());
        assertEquals("", configObject.getUrl());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法（Lombok生成）
     */
    @Test
    @DisplayName("Lombok验证 - equals和hashCode方法")
    void testMenuItemConfigObject_EqualsAndHashCode() {
        // 准备测试数据
        MenuItemConfigObject configObject1 = new MenuItemConfigObject();
        configObject1.setApiName("Account");
        configObject1.setDisplayName("客户");
        configObject1.setNumber(1);

        MenuItemConfigObject configObject2 = new MenuItemConfigObject();
        configObject2.setApiName("Account");
        configObject2.setDisplayName("客户");
        configObject2.setNumber(1);

        MenuItemConfigObject configObject3 = new MenuItemConfigObject();
        configObject3.setApiName("Contact");
        configObject3.setDisplayName("联系人");
        configObject3.setNumber(2);

        // 验证equals
        assertEquals(configObject1, configObject2);
        assertNotEquals(configObject1, configObject3);
        
        // 验证hashCode
        assertEquals(configObject1.hashCode(), configObject2.hashCode());
        
        // 验证toString
        assertNotNull(configObject1.toString());
        assertTrue(configObject1.toString().contains("Account"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试完整的使用场景
     */
    @Test
    @DisplayName("集成场景 - 完整的菜单项配置使用场景")
    void testMenuItemConfigObject_CompleteUsageScenario() {
        // 准备测试数据 - 模拟完整的菜单项配置
        MenuItemConfigObject configObject = new MenuItemConfigObject(true, false, "all");
        
        // 设置基本信息
        configObject.setApiName("Account");
        configObject.setDisplayName("客户管理");
        configObject.setNumber(1);
        configObject.setIconIndex(100);
        configObject.setIconPathHome("/icons/home/<USER>");
        configObject.setIconPathMenu("/icons/menu/account.svg");
        configObject.setItemType("predef_obj");
        configObject.setUrl("/account/list");
        configObject.setUseDefaultUrl(true);
        
        // 设置移动端配置
        MenuItemConfigObject.MobileConfig mobileConfig = new MenuItemConfigObject.MobileConfig();
        mobileConfig.setMobileAddAction("mobile_add_account");
        mobileConfig.setMobileListAction("mobile_list_account");
        configObject.setMobileConfig(mobileConfig);

        // 验证完整配置
        assertEquals("Account", configObject.getApiName());
        assertEquals("客户管理", configObject.getDisplayName());
        assertEquals(Integer.valueOf(1), configObject.getNumber());
        assertEquals(Integer.valueOf(100), configObject.getIconIndex());
        assertEquals("/icons/home/<USER>", configObject.getIconPathHome());
        assertEquals("/icons/menu/account.svg", configObject.getIconPathMenu());
        assertEquals("all", configObject.getDeviceType());
        assertEquals("predef_obj", configObject.getItemType());
        assertEquals(true, configObject.getValidatePrivilege());
        assertEquals(false, configObject.getValidateDescribe());
        assertEquals("/account/list", configObject.getUrl());
        assertEquals(true, configObject.getUseDefaultUrl());
        
        // 验证移动端配置
        assertNotNull(configObject.getMobileConfig());
        assertEquals("mobile_add_account", configObject.getMobileConfig().getMobileAddAction());
        assertEquals("mobile_list_account", configObject.getMobileConfig().getMobileListAction());
        
        // 验证JSON序列化往返
        String jsonString = JSON.toJSONString(configObject);
        MenuItemConfigObject deserializedObject = JSON.parseObject(jsonString, MenuItemConfigObject.class);
        assertEquals(configObject.getApiName(), deserializedObject.getApiName());
        assertEquals(configObject.getDisplayName(), deserializedObject.getDisplayName());
        assertEquals(configObject.getMobileConfig().getMobileAddAction(), 
                deserializedObject.getMobileConfig().getMobileAddAction());
    }
}
