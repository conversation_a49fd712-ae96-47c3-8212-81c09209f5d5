package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.facishare.paas.common.util.UdobjConstants.SUPPORT_ACTION_CLASS_NAME_EXT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DefObjUtil工具类的单元测试 测试对象描述和字段描述的处理功能
 */
@ExtendWith(MockitoExtension.class)
class DefObjUtilTest {

    @InjectMocks
    private DefObjUtil defObjUtil;

    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockFieldDescribe1;
    private IFieldDescribe mockFieldDescribe2;
    private IFieldDescribe mockFieldDescribe3;

    @BeforeEach
    void setUp() {
        mockObjectDescribe = mock(IObjectDescribe.class);
        mockFieldDescribe1 = mock(IFieldDescribe.class);
        mockFieldDescribe2 = mock(IFieldDescribe.class);
        mockFieldDescribe3 = mock(IFieldDescribe.class);
    }

    /**
     * GenerateByAI 测试内容描述：测试为字段描述添加创建时间的正常场景
     */
    @Test
    @DisplayName("正常场景 - 为字段描述添加创建时间成功")
    void testAddFieldDescribeCreateTime_Success() {
        // 准备测试数据
        List<IFieldDescribe> fieldDescribes = Arrays.asList(mockFieldDescribe1, mockFieldDescribe2, mockFieldDescribe3);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        // 记录调用前的时间
        long beforeTime = System.currentTimeMillis();

        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 记录调用后的时间
        long afterTime = System.currentTimeMillis();

        // 验证结果
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();
        verify(mockFieldDescribe3).getCreateTime();

        // 验证setCreateTime被调用，并且时间在合理范围内
        verify(mockFieldDescribe1).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe2).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe3).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
    }

    /**
     * GenerateByAI 测试内容描述：测试为字段描述添加创建时间时，部分字段已有创建时间的场景
     */
    @Test
    @DisplayName("正常场景 - 为字段描述添加创建时间时部分字段已有创建时间")
    void testAddFieldDescribeCreateTime_PartialExistingTime() {
        // 准备测试数据
        List<IFieldDescribe> fieldDescribes = Arrays.asList(mockFieldDescribe1, mockFieldDescribe2, mockFieldDescribe3);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(1000L); // 已有创建时间
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);  // 没有创建时间
        when(mockFieldDescribe3.getCreateTime()).thenReturn(2000L); // 已有创建时间

        // 记录调用前的时间
        long beforeTime = System.currentTimeMillis();

        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 记录调用后的时间
        long afterTime = System.currentTimeMillis();

        // 验证结果
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();
        verify(mockFieldDescribe3).getCreateTime();

        // 验证只有没有创建时间的字段被设置了创建时间
        verify(mockFieldDescribe1, never()).setCreateTime(anyLong());
        verify(mockFieldDescribe2).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe3, never()).setCreateTime(anyLong());
    }

    /**
     * GenerateByAI 测试内容描述：测试为字段描述添加创建时间时，所有字段都已有创建时间的场景
     */
    @Test
    @DisplayName("正常场景 - 为字段描述添加创建时间时所有字段都已有创建时间")
    void testAddFieldDescribeCreateTime_AllExistingTime() {
        // 准备测试数据
        List<IFieldDescribe> fieldDescribes = Arrays.asList(mockFieldDescribe1, mockFieldDescribe2);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(1000L);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(2000L);

        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 验证结果
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();

        // 验证没有字段被设置创建时间
        verify(mockFieldDescribe1, never()).setCreateTime(anyLong());
        verify(mockFieldDescribe2, never()).setCreateTime(anyLong());
    }

    /**
     * GenerateByAI 测试内容描述：测试为字段描述添加创建时间时，字段列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - 为字段描述添加创建时间时字段列表为空")
    void testAddFieldDescribeCreateTime_EmptyFieldList() {
        // 准备测试数据
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Collections.emptyList());

        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 验证结果
        verify(mockObjectDescribe).getFieldDescribes();
        // 没有字段，所以不应该有任何setCreateTime调用
    }

    /**
     * GenerateByAI 测试内容描述：测试为字段描述添加创建时间时，对象描述为null的场景
     */
    @Test
    @DisplayName("边界场景 - 为字段描述添加创建时间时对象描述为null")
    void testAddFieldDescribeCreateTime_NullObjectDescribe() {
        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(null);

        // 验证结果 - 方法应该直接返回，不抛出异常
        // 没有异常被抛出就说明测试通过
    }

    /**
     * GenerateByAI 测试内容描述：测试创建时间的递增特性
     */
    @Test
    @DisplayName("功能验证 - 验证创建时间的递增特性")
    void testAddFieldDescribeCreateTime_IncrementalTime() {
        // 准备测试数据
        List<IFieldDescribe> fieldDescribes = Arrays.asList(mockFieldDescribe1, mockFieldDescribe2, mockFieldDescribe3);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        // 使用ArgumentCaptor来捕获设置的时间值
        org.mockito.ArgumentCaptor<Long> timeCaptor1 = org.mockito.ArgumentCaptor.forClass(Long.class);
        org.mockito.ArgumentCaptor<Long> timeCaptor2 = org.mockito.ArgumentCaptor.forClass(Long.class);
        org.mockito.ArgumentCaptor<Long> timeCaptor3 = org.mockito.ArgumentCaptor.forClass(Long.class);

        // 执行测试
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 验证并捕获时间值
        verify(mockFieldDescribe1).setCreateTime(timeCaptor1.capture());
        verify(mockFieldDescribe2).setCreateTime(timeCaptor2.capture());
        verify(mockFieldDescribe3).setCreateTime(timeCaptor3.capture());

        // 验证时间是递增的
        Long time1 = timeCaptor1.getValue();
        Long time2 = timeCaptor2.getValue();
        Long time3 = timeCaptor3.getValue();

        assertNotNull(time1);
        assertNotNull(time2);
        assertNotNull(time3);

        // 验证时间递增（由于System.currentTimeMillis()可能在调用间变化，我们验证相对递增）
        // 第一个字段: currentTime + 0
        // 第二个字段: currentTime + 1
        // 第三个字段: currentTime + 2
        // 但由于currentTime可能变化，我们只验证相对差值
        assertTrue(time2 >= time1, "第二个时间应该大于等于第一个时间");
        assertTrue(time3 >= time2, "第三个时间应该大于等于第二个时间");

        // 验证时间差在合理范围内（考虑到System.currentTimeMillis()的变化）
        long diff1 = time2 - time1;
        long diff2 = time3 - time2;
        assertTrue(diff1 >= 0 && diff1 <= 10, "时间差应该在合理范围内");
        assertTrue(diff2 >= 0 && diff2 <= 10, "时间差应该在合理范围内");
    }

    /**
     * GenerateByAI 测试内容描述：测试根据支持代码获取动作类名的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据支持代码获取动作类名成功")
    void testGetActionClassNameByActionCode_Success() {
        // 准备测试数据
        String supportCode = "testAction";

        // 执行测试
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // 验证结果
        assertEquals(supportCode + SUPPORT_ACTION_CLASS_NAME_EXT, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试根据支持代码获取动作类名时，支持代码为空的场景
     */
    @Test
    @DisplayName("边界场景 - 根据支持代码获取动作类名时支持代码为空")
    void testGetActionClassNameByActionCode_EmptyCode() {
        // 准备测试数据
        String supportCode = "";

        // 执行测试
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // 验证结果
        assertEquals("" + SUPPORT_ACTION_CLASS_NAME_EXT, result);
        assertEquals(SUPPORT_ACTION_CLASS_NAME_EXT, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试根据支持代码获取动作类名时，支持代码为null的场景
     */
    @Test
    @DisplayName("边界场景 - 根据支持代码获取动作类名时支持代码为null")
    void testGetActionClassNameByActionCode_NullCode() {
        // 执行测试
        String result = DefObjUtil.getActionClassNameByActionCode(null);

        // 验证结果
        assertEquals("null" + SUPPORT_ACTION_CLASS_NAME_EXT, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试根据支持代码获取动作类名时，支持代码包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 根据支持代码获取动作类名时支持代码包含特殊字符")
    void testGetActionClassNameByActionCode_SpecialCharacters() {
        // 准备测试数据
        String supportCode = "test-action_v1.0";

        // 执行测试
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // 验证结果
        assertEquals(supportCode + SUPPORT_ACTION_CLASS_NAME_EXT, result);
    }

    /**
     * GenerateByAI 测试内容描述：验证常量的使用
     */
    @Test
    @DisplayName("常量验证 - 验证SUPPORT_ACTION_CLASS_NAME_EXT常量的使用")
    void testConstantUsage() {
        // 验证常量不为空
        assertNotNull(SUPPORT_ACTION_CLASS_NAME_EXT);
        assertFalse(SUPPORT_ACTION_CLASS_NAME_EXT.isEmpty());

        // 测试常量的实际使用
        String testCode = "test";
        String result = DefObjUtil.getActionClassNameByActionCode(testCode);
        assertTrue(result.endsWith(SUPPORT_ACTION_CLASS_NAME_EXT));
        assertTrue(result.startsWith(testCode));
    }

    /**
     * GenerateByAI 测试内容描述：测试方法的静态特性
     */
    @Test
    @DisplayName("静态方法验证 - 验证getActionClassNameByActionCode是静态方法")
    void testStaticMethodBehavior() {
        // 测试静态方法的一致性
        String supportCode = "staticTest";

        String result1 = DefObjUtil.getActionClassNameByActionCode(supportCode);
        String result2 = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // 静态方法应该返回相同的结果
        assertEquals(result1, result2);
        assertNotSame(result1, result2); // 不同的字符串对象
    }

    /**
     * GenerateByAI 测试内容描述：测试方法的不可变性
     */
    @Test
    @DisplayName("不可变性验证 - 验证相同输入产生相同输出")
    void testMethodImmutability() {
        // 测试addFieldDescribeCreateTime方法的幂等性（在已有时间的情况下）
        List<IFieldDescribe> fieldDescribes = Arrays.asList(mockFieldDescribe1);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(1000L);

        // 多次调用
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // 验证setCreateTime从未被调用（因为已有时间）
        verify(mockFieldDescribe1, never()).setCreateTime(anyLong());

        // 测试getActionClassNameByActionCode方法的一致性
        String code = "test";
        String result1 = DefObjUtil.getActionClassNameByActionCode(code);
        String result2 = DefObjUtil.getActionClassNameByActionCode(code);
        assertEquals(result1, result2);
    }
}
