package com.facishare.paas.appframework.metadata.cache;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;
import java.util.function.UnaryOperator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RedissonServiceImpl的单元测试
 * 测试Redisson服务实现的功能
 */
@ExtendWith(MockitoExtension.class)
class RedissonServiceImplTest {

    @Mock
    private RedissonClient mockRedissonClient;

    @Mock
    private RLock mockLock;

    @Mock
    private RBucket<Object> mockBucket;

    @Mock
    private User mockUser;

    @InjectMocks
    private RedissonServiceImpl redissonService;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
        lenient().when(mockRedissonClient.getLock(anyString())).thenReturn(mockLock);
        lenient().when(mockRedissonClient.getBucket(anyString(), any())).thenReturn(mockBucket);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLock的正常场景
     */
    @Test
    @DisplayName("正常场景 - tryLock获取锁成功")
    void testTryLock_Success() throws InterruptedException {
        // 准备测试数据
        String key = "testKey";
        long waitTime = 5;
        long leaseTime = 10;
        TimeUnit unit = TimeUnit.SECONDS;

        when(mockLock.tryLock(waitTime, leaseTime, unit)).thenReturn(true);

        // 执行测试
        RLock result = redissonService.tryLock(waitTime, leaseTime, unit, key);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockLock, result);
        verify(mockRedissonClient).getLock(key);
        verify(mockLock).tryLock(waitTime, leaseTime, unit);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLock获取锁失败的场景
     */
    @Test
    @DisplayName("边界场景 - tryLock获取锁失败")
    void testTryLock_Failed() throws InterruptedException {
        // 准备测试数据
        String key = "testKey";
        long waitTime = 5;
        long leaseTime = 10;
        TimeUnit unit = TimeUnit.SECONDS;

        when(mockLock.tryLock(waitTime, leaseTime, unit)).thenReturn(false);

        // 执行测试
        RLock result = redissonService.tryLock(waitTime, leaseTime, unit, key);

        // 验证结果
        assertNull(result);
        verify(mockRedissonClient).getLock(key);
        verify(mockLock).tryLock(waitTime, leaseTime, unit);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLock时发生InterruptedException
     */
    @Test
    @DisplayName("异常场景 - tryLock发生InterruptedException")
    void testTryLock_InterruptedException() throws InterruptedException {
        // 准备测试数据
        String key = "testKey";
        long waitTime = 5;
        long leaseTime = 10;
        TimeUnit unit = TimeUnit.SECONDS;

        when(mockLock.tryLock(waitTime, leaseTime, unit)).thenThrow(new InterruptedException("Test interrupt"));

        // 执行测试
        RLock result = redissonService.tryLock(waitTime, leaseTime, unit, key);

        // 验证结果
        assertNull(result);
        // 验证线程中断状态被恢复
        assertTrue(Thread.currentThread().isInterrupted());
        // 清除中断状态以免影响其他测试
        Thread.interrupted();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLockWithErrorMsg的正常场景
     */
    @Test
    @DisplayName("正常场景 - tryLockWithErrorMsg获取锁成功")
    void testTryLockWithErrorMsg_Success() throws InterruptedException {
        // 准备测试数据
        String key = "testKey";
        String message = "获取锁失败";
        long waitTime = 5;
        long leaseTime = 10;
        TimeUnit unit = TimeUnit.SECONDS;

        when(mockLock.tryLock(waitTime, leaseTime, unit)).thenReturn(true);

        // 执行测试
        RLock result = redissonService.tryLockWithErrorMsg(waitTime, leaseTime, unit, key, message);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockLock, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLockWithErrorMsg获取锁失败抛出异常
     */
    @Test
    @DisplayName("异常场景 - tryLockWithErrorMsg获取锁失败抛出异常")
    void testTryLockWithErrorMsg_ThrowsException() throws InterruptedException {
        // 准备测试数据
        String key = "testKey";
        String message = "获取锁失败";
        long waitTime = 5;
        long leaseTime = 10;
        TimeUnit unit = TimeUnit.SECONDS;

        when(mockLock.tryLock(waitTime, leaseTime, unit)).thenReturn(false);

        // 执行测试并验证异常
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            redissonService.tryLockWithErrorMsg(waitTime, leaseTime, unit, key, message);
        });

        assertEquals(message, exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试tryLock(User, String, String)的正常场景
     */
    @Test
    @DisplayName("正常场景 - tryLock用户级别锁获取成功")
    void testTryLockWithUser_Success() throws InterruptedException {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Mock配置
            mockedConfig.when(AppFrameworkConfig::getDuplicateSearchLockWaitTimeSeconds).thenReturn(30L);
            mockedConfig.when(AppFrameworkConfig::getDuplicateSearchLockWaitLeaseTimeSeconds).thenReturn(60L);

            // 准备测试数据
            String describeApiName = "Account";
            String key = "testKey";

            when(mockLock.tryLock(30L, 60L, TimeUnit.SECONDS)).thenReturn(true);

            // 执行测试
            RLock result = redissonService.tryLock(mockUser, describeApiName, key);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockLock, result);

            // 验证生成的key格式
            String expectedKey = String.format("%s_%s_%s_%s", 
                RedissonServiceImpl.REDISSON_PRE, "testTenant", describeApiName, key);
            verify(mockRedissonClient).getLock(expectedKey);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isLocked的正常场景
     */
    @Test
    @DisplayName("正常场景 - isLocked检查锁状态")
    void testIsLocked_Success() {
        // 准备测试数据
        String key = "testKey";

        when(mockLock.isLocked()).thenReturn(true);

        // 执行测试
        boolean result = redissonService.isLocked(key);

        // 验证结果
        assertTrue(result);
        verify(mockRedissonClient).getLock(key);
        verify(mockLock).isLocked();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试unlock的正常场景
     */
    @Test
    @DisplayName("正常场景 - unlock释放锁成功")
    void testUnlock_Success() {
        // 执行测试
        assertDoesNotThrow(() -> {
            redissonService.unlock(mockLock);
        });

        // 验证调用了unlock方法
        verify(mockLock).unlock();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试unlock时传入null锁
     */
    @Test
    @DisplayName("边界场景 - unlock传入null锁")
    void testUnlock_NullLock() {
        // 执行测试
        assertDoesNotThrow(() -> {
            redissonService.unlock(null);
        });

        // 验证没有调用任何方法
        verifyNoInteractions(mockLock);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试unlock时发生异常
     */
    @Test
    @DisplayName("异常场景 - unlock时发生异常")
    void testUnlock_Exception() {
        // Mock unlock抛出异常
        doThrow(new RuntimeException("Unlock error")).when(mockLock).unlock();

        // 执行测试 - 应该捕获异常并记录日志
        assertDoesNotThrow(() -> {
            redissonService.unlock(mockLock);
        });

        verify(mockLock).unlock();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试limiter的正常场景 - 首次访问
     */
    @Test
    @DisplayName("正常场景 - limiter首次访问限流")
    void testLimiter_FirstAccess() {
        // 准备测试数据
        String key = "limiterKey";
        TimeUnit unit = TimeUnit.MINUTES;
        int max = 10;

        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.trySet(any(), eq(1L), eq(unit))).thenReturn(true);

        // 执行测试
        boolean result = redissonService.limiter(key, unit, max);

        // 验证结果
        assertTrue(result);
        verify(mockBucket).get();
        verify(mockBucket).trySet(any(), eq(1L), eq(unit));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试incrementAndGet的正常场景
     */
    @Test
    @DisplayName("正常场景 - incrementAndGet原子递增")
    void testIncrementAndGet_Success() {
        // 准备测试数据
        String key = "counterKey";
        long timeToLive = 60;
        TimeUnit timeUnit = TimeUnit.SECONDS;
        UnaryOperator<Integer> incrementFunction = value -> value + 1;
        Integer defaultValue = 0;

        when(mockBucket.get()).thenReturn(5);
        when(mockBucket.compareAndSet(5, 6)).thenReturn(true);

        // 执行测试
        Integer result = redissonService.incrementAndGet(key, timeToLive, timeUnit, incrementFunction, defaultValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(6, result);
        verify(mockBucket).get();
        verify(mockBucket).compareAndSet(5, 6);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试incrementAndGet时值为null的场景
     */
    @Test
    @DisplayName("边界场景 - incrementAndGet值为null使用默认值")
    void testIncrementAndGet_NullValue() {
        // 准备测试数据
        String key = "counterKey";
        long timeToLive = 60;
        TimeUnit timeUnit = TimeUnit.SECONDS;
        UnaryOperator<Integer> incrementFunction = value -> value + 1;
        Integer defaultValue = 0;

        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.compareAndSet(null, 1)).thenReturn(true);

        // 执行测试
        Integer result = redissonService.incrementAndGet(key, timeToLive, timeUnit, incrementFunction, defaultValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result);
        verify(mockBucket).get();
        verify(mockBucket).compareAndSet(null, 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLock的正常场景
     */
    @Test
    @DisplayName("正常场景 - getLock获取锁对象")
    void testGetLock_Success() {
        // 准备测试数据
        String key = "testKey";

        // 执行测试
        RLock result = redissonService.getLock(key);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockLock, result);
        verify(mockRedissonClient).getLock(key);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(redissonService);
        assertNotNull(mockRedissonClient);
        assertNotNull(mockUser);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量定义
     */
    @Test
    @DisplayName("常量测试 - 验证REDISSON_PRE常量")
    void testConstants() {
        assertEquals("REDISSON_KEY", RedissonServiceImpl.REDISSON_PRE);
    }
}
