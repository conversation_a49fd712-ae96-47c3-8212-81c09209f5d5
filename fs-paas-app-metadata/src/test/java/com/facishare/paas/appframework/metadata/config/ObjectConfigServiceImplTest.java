package com.facishare.paas.appframework.metadata.config;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ObjectConfigServiceImpl的单元测试
 * 测试对象配置服务的功能
 */
@ExtendWith(MockitoExtension.class)
class ObjectConfigServiceImplTest {

    @Mock
    private ObjectConfigManager mockConfigManager;

    @Mock
    private ObjectConfigProvider mockConfigProvider;

    @Mock
    private BusinessFilterProvider mockBusinessFilterProvider;

    @Mock
    private DescribeLogicService mockDescribeLogicService;

    @InjectMocks
    private ObjectConfigServiceImpl objectConfigService;

    private User testUser;
    private Set<String> testApiNameList;

    @BeforeEach
    void setUp() {
        // 构造测试用户
        testUser = User.systemUser("74255");
        
        // 构造测试API名称列表
        testApiNameList = Sets.newHashSet("Account", "Contact", "Opportunity");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置的正常场景
     */
    @Test
    @DisplayName("获取对象配置 - 正常场景")
    void testGetObjectConfig_正常场景() {
        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(Maps.newHashMap());

        // 执行被测试方法
        Map<String, ObjectConfig> result = objectConfigService.getObjectConfig(testUser, testApiNameList);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时用户为null
     */
    @Test
    @DisplayName("获取对象配置 - 用户为null")
    void testGetObjectConfig_用户为null() {
        // 执行被测试方法并验证异常
        assertThrows(Exception.class, () -> {
            objectConfigService.getObjectConfig(null, testApiNameList);
        });
        
        // 验证没有调用其他服务
        verify(mockDescribeLogicService, never()).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时API名称列表为空
     */
    @Test
    @DisplayName("获取对象配置 - API名称列表为空")
    void testGetObjectConfig_API名称列表为空() {
        // 准备测试数据
        Set<String> emptyApiNameList = Sets.newHashSet();
        
        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(Maps.newHashMap());
        
        // 执行被测试方法
        Map<String, ObjectConfig> result = objectConfigService.getObjectConfig(testUser, emptyApiNameList);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时服务抛出异常
     */
    @Test
    @DisplayName("获取对象配置 - 服务抛出异常")
    void testGetObjectConfig_服务抛出异常() {
        // 配置Mock行为 - 抛出异常
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenThrow(new RuntimeException("服务异常"));
        
        // 执行被测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            objectConfigService.getObjectConfig(testUser, testApiNameList);
        });
        
        // 验证异常信息
        assertEquals("服务异常", exception.getMessage());
        
        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectConfigService);
        assertNotNull(mockConfigManager);
        assertNotNull(mockConfigProvider);
        assertNotNull(mockBusinessFilterProvider);
        assertNotNull(mockDescribeLogicService);
    }
}
