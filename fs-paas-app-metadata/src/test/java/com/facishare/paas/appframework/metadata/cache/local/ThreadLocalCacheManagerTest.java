package com.facishare.paas.appframework.metadata.cache.local;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;

import java.util.Collection;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ThreadLocalCacheManager的单元测试
 * 测试线程本地缓存管理器的功能
 */
@ExtendWith(MockitoExtension.class)
class ThreadLocalCacheManagerTest {

    private ThreadLocalCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        cacheManager = new ThreadLocalCacheManager();
    }

    @AfterEach
    void tearDown() {
        // 清理ThreadLocal以避免内存泄漏
        try {
            // 通过反射清理ThreadLocal
            java.lang.reflect.Field field = ThreadLocalCacheManager.class.getDeclaredField("THREAD_LOCAL");
            field.setAccessible(true);
            ThreadLocal<?> threadLocal = (ThreadLocal<?>) field.get(null);
            threadLocal.remove();
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - init初始化监听器")
    void testInit_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 执行测试
            assertDoesNotThrow(() -> {
                cacheManager.init();
            });

            // 验证添加了监听器
            mockedRequestContextManager.verify(() -> RequestContextManager.addContextAddListener(any()));
            mockedRequestContextManager.verify(() -> RequestContextManager.addContextRemoveListener(any()));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCache的正常场景
     */
    @Test
    @DisplayName("正常场景 - getCache获取缓存成功")
    void testGetCache_Success() {
        // 准备测试数据
        String cacheName = "testCache";

        // 执行测试
        Cache result = cacheManager.getCache(cacheName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof CacheWrapper);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCache的缓存复用场景
     */
    @Test
    @DisplayName("正常场景 - getCache缓存复用")
    void testGetCache_CacheReuse() {
        // 准备测试数据
        String cacheName = "testCache";

        // 执行测试 - 多次获取同一个缓存
        Cache cache1 = cacheManager.getCache(cacheName);
        Cache cache2 = cacheManager.getCache(cacheName);

        // 验证结果 - 应该是不同的CacheWrapper实例，但包装的是同一个底层缓存
        assertNotNull(cache1);
        assertNotNull(cache2);
        assertNotSame(cache1, cache2); // CacheWrapper是每次新创建的
        assertTrue(cache1 instanceof CacheWrapper);
        assertTrue(cache2 instanceof CacheWrapper);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCache时传入null参数
     */
    @Test
    @DisplayName("边界场景 - getCache传入null参数")
    void testGetCache_NullName() {
        // 执行测试
        Cache result = cacheManager.getCache(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof CacheWrapper);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCache时传入空字符串
     */
    @Test
    @DisplayName("边界场景 - getCache传入空字符串")
    void testGetCache_EmptyName() {
        // 准备测试数据
        String cacheName = "";

        // 执行测试
        Cache result = cacheManager.getCache(cacheName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof CacheWrapper);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCacheNames的正常场景
     */
    @Test
    @DisplayName("正常场景 - getCacheNames获取缓存名称列表")
    void testGetCacheNames_Success() {
        // 准备测试数据 - 先创建一些缓存
        cacheManager.getCache("cache1");
        cacheManager.getCache("cache2");
        cacheManager.getCache("cache3");

        // 执行测试
        Collection<String> result = cacheManager.getCacheNames();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("cache1"));
        assertTrue(result.contains("cache2"));
        assertTrue(result.contains("cache3"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCacheNames时没有缓存的场景
     */
    @Test
    @DisplayName("边界场景 - getCacheNames没有缓存时返回空集合")
    void testGetCacheNames_EmptyCache() {
        // 执行测试
        Collection<String> result = cacheManager.getCacheNames();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程隔离性
     */
    @Test
    @DisplayName("并发场景 - 测试线程隔离性")
    void testThreadIsolation() throws InterruptedException {
        // 准备测试数据
        ExecutorService executor = Executors.newFixedThreadPool(2);
        CountDownLatch latch = new CountDownLatch(2);
        
        String[] thread1CacheNames = new String[1];
        String[] thread2CacheNames = new String[1];

        try {
            // 线程1
            executor.submit(() -> {
                try {
                    cacheManager.getCache("thread1Cache");
                    Collection<String> cacheNames = cacheManager.getCacheNames();
                    thread1CacheNames[0] = cacheNames.toString();
                } finally {
                    latch.countDown();
                }
            });

            // 线程2
            executor.submit(() -> {
                try {
                    cacheManager.getCache("thread2Cache");
                    Collection<String> cacheNames = cacheManager.getCacheNames();
                    thread2CacheNames[0] = cacheNames.toString();
                } finally {
                    latch.countDown();
                }
            });

            // 等待所有线程完成
            assertTrue(latch.await(5, TimeUnit.SECONDS));

            // 验证线程隔离性
            assertNotNull(thread1CacheNames[0]);
            assertNotNull(thread2CacheNames[0]);
            assertTrue(thread1CacheNames[0].contains("thread1Cache"));
            assertFalse(thread1CacheNames[0].contains("thread2Cache"));
            assertTrue(thread2CacheNames[0].contains("thread2Cache"));
            assertFalse(thread2CacheNames[0].contains("thread1Cache"));

        } finally {
            executor.shutdown();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试缓存操作的基本功能
     */
    @Test
    @DisplayName("功能场景 - 测试缓存的基本操作")
    void testCacheBasicOperations() {
        // 准备测试数据
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        // 获取缓存
        Cache cache = cacheManager.getCache(cacheName);

        // 执行缓存操作
        cache.put(key, value);
        Cache.ValueWrapper result = cache.get(key);

        // 验证结果
        assertNotNull(result);
        assertEquals(value, result.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个缓存的独立性
     */
    @Test
    @DisplayName("功能场景 - 测试多个缓存的独立性")
    void testMultipleCacheIndependence() {
        // 准备测试数据
        String cache1Name = "cache1";
        String cache2Name = "cache2";
        String key = "sameKey";
        String value1 = "value1";
        String value2 = "value2";

        // 获取两个不同的缓存
        Cache cache1 = cacheManager.getCache(cache1Name);
        Cache cache2 = cacheManager.getCache(cache2Name);

        // 在两个缓存中存储不同的值
        cache1.put(key, value1);
        cache2.put(key, value2);

        // 验证缓存独立性
        Cache.ValueWrapper result1 = cache1.get(key);
        Cache.ValueWrapper result2 = cache2.get(key);

        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(value1, result1.get());
        assertEquals(value2, result2.get());
        assertNotEquals(result1.get(), result2.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CacheName枚举
     */
    @Test
    @DisplayName("枚举测试 - 验证CacheName枚举值")
    void testCacheNameEnum() {
        // 验证枚举值存在
        assertNotNull(ThreadLocalCacheManager.CacheName.BUTTON);
        assertNotNull(ThreadLocalCacheManager.CacheName.DUPLICATED_SEARCH);
        assertNotNull(ThreadLocalCacheManager.CacheName.SEARCH_TEMPLATE);

        // 验证枚举值数量
        ThreadLocalCacheManager.CacheName[] values = ThreadLocalCacheManager.CacheName.values();
        assertEquals(3, values.length);

        // 验证枚举名称
        assertEquals("BUTTON", ThreadLocalCacheManager.CacheName.BUTTON.name());
        assertEquals("DUPLICATED_SEARCH", ThreadLocalCacheManager.CacheName.DUPLICATED_SEARCH.name());
        assertEquals("SEARCH_TEMPLATE", ThreadLocalCacheManager.CacheName.SEARCH_TEMPLATE.name());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试性能场景
     */
    @Test
    @DisplayName("性能场景 - 测试大量缓存创建性能")
    void testPerformance_MassiveCacheCreation() {
        // 测试创建大量缓存的性能
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 1000; i++) {
            Cache cache = cacheManager.getCache("cache" + i);
            cache.put("key" + i, "value" + i);
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证性能 - 1000个缓存创建应该在合理时间内完成
        assertTrue(executionTime < 1000, "创建1000个缓存耗时过长: " + executionTime + "ms");

        // 验证缓存数量
        Collection<String> cacheNames = cacheManager.getCacheNames();
        assertEquals(1000, cacheNames.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试类的基本属性
     */
    @Test
    @DisplayName("基本属性测试 - 验证类的基本特性")
    void testBasicProperties() {
        // 验证类不为null
        assertNotNull(cacheManager);
        
        // 验证类名
        assertEquals("ThreadLocalCacheManager", cacheManager.getClass().getSimpleName());
        
        // 验证包名
        assertEquals("com.facishare.paas.appframework.metadata.cache.local", 
                    cacheManager.getClass().getPackage().getName());
        
        // 验证实现了CacheManager接口
        assertTrue(cacheManager instanceof org.springframework.cache.CacheManager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化
     */
    @Test
    @DisplayName("测试Service实例化")
    void testServiceInstantiation() {
        // Assert
        assertNotNull(cacheManager);
    }
}
