package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.IFilter;import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GoalValueInitSceneProvider的单元测试
 * 测试目标值场景初始化提供者的功能
 */
@ExtendWith(MockitoExtension.class)
class GoalValueInitSceneProviderTest {

    @Mock
    private ISearchTemplateService searchTemplateService;

    @InjectMocks
    private GoalValueInitSceneProvider goalValueInitSceneProvider;

    private User testUser;
    private List<ISearchTemplate> mockTemplates;

    @BeforeEach
    void setUp() throws MetadataServiceException {
        // 创建测试用户
        testUser = new User("74255", "1000");
        
        // 注入searchTemplateService到父类
        ReflectionTestUtils.setField(goalValueInitSceneProvider, "searchTemplateService", searchTemplateService);
        
        // 准备Mock模板列表
        mockTemplates = new ArrayList<>();
        ISearchTemplate mockTemplate = mock(ISearchTemplate.class);
        when(mockTemplate.getTenantId()).thenReturn("74255");
        when(mockTemplate.getApiName()).thenReturn("ALL");
        mockTemplates.add(mockTemplate);
        
        // 配置默认Mock行为
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenReturn(mockTemplates);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getApiName方法返回正确的API名称
     */
    @Test
    @DisplayName("正常场景 - getApiName返回正确的API名称")
    void testGetApiName_ReturnsCorrectApiName() {
        // 执行测试
        String result = goalValueInitSceneProvider.getApiName();

        // 验证结果
        assertNotNull(result);
        assertEquals(Utils.GOAL_VALUE_API_NAME, result);
        assertEquals("GoalValueObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法的正常场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList返回正确的搜索模板列表")
    void testGetDefaultSearchTemplateList_ReturnsCorrectTemplates() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "GoalValueObj";
        String extendAttribute = null;

        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTemplates, result);
        assertEquals(1, result.size());

        // 验证调用了正确的服务方法
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(
            eq("74255"), 
            eq("GoalValueObj"), 
            argThat(codes -> codes != null && codes.contains(SearchTemplateCode.ALL))
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法验证搜索模板代码
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList验证搜索模板代码参数")
    void testGetDefaultSearchTemplateList_ValidatesSearchTemplateCodes() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "GoalValueObj";

        // 执行测试
        goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, null);

        // 验证调用参数
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(
            eq("74255"), 
            eq("GoalValueObj"), 
            argThat(codes -> {
                Set<SearchTemplateCode> codeSet = (Set<SearchTemplateCode>) codes;
                return codeSet.size() == 1 && codeSet.contains(SearchTemplateCode.ALL);
            })
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理不同用户的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理不同租户用户")
    void testGetDefaultSearchTemplateList_DifferentTenantUser() throws MetadataServiceException {
        // 准备测试数据 - 不同租户的用户
        User differentTenantUser = new User("78057", "2000");
        String apiName = "GoalValueObj";

        // 配置Mock返回不同租户的模板
        List<ISearchTemplate> mockDifferentTenantTemplates = new ArrayList<>();
        ISearchTemplate mockTemplate = mock(ISearchTemplate.class);
        when(mockTemplate.getTenantId()).thenReturn("78057");
        when(mockTemplate.getApiName()).thenReturn("ALL");
        mockDifferentTenantTemplates.add(mockTemplate);
        
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(eq("78057"), anyString(), any()))
            .thenReturn(mockDifferentTenantTemplates);

        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(differentTenantUser, apiName, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockDifferentTenantTemplates, result);
        assertEquals(1, result.size());
        
        // 验证调用了正确租户的服务
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("78057"), eq("GoalValueObj"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理服务异常的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理服务异常返回空列表")
    void testGetDefaultSearchTemplateList_HandlesServiceException() throws MetadataServiceException {
        // 配置Mock抛出异常
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenThrow(new RuntimeException("Service error"));

        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, "GoalValueObj", null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证异常被正确处理
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), eq("GoalValueObj"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理运行时异常的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理运行时异常返回空列表")
    void testGetDefaultSearchTemplateList_HandlesRuntimeException() throws MetadataServiceException {
        // 配置Mock抛出运行时异常
        when(searchTemplateService.findByObjectDescribeAPINameAndCode(anyString(), anyString(), any()))
            .thenThrow(new RuntimeException("Runtime error"));

        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, "GoalValueObj", null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证异常被正确处理
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), eq("GoalValueObj"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null API名称的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理null API名称")
    void testGetDefaultSearchTemplateList_NullApiName() throws MetadataServiceException {
        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTemplates, result);
        
        // 验证调用了服务方法，传入null API名称
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), isNull(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理null用户的场景
     */
    @Test
    @DisplayName("异常场景 - getDefaultSearchTemplateList处理null用户抛出异常")
    void testGetDefaultSearchTemplateListThrowsNullPointerException_NullUser() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            goalValueInitSceneProvider.getDefaultSearchTemplateList(null, "GoalValueObj", null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理空字符串API名称的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理空字符串API名称")
    void testGetDefaultSearchTemplateList_EmptyApiName() throws MetadataServiceException {
        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, "", null);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTemplates, result);
        
        // 验证调用了服务方法，传入空字符串API名称
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), eq(""), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultSearchTemplateList方法处理扩展属性参数的场景
     */
    @Test
    @DisplayName("正常场景 - getDefaultSearchTemplateList处理扩展属性参数")
    void testGetDefaultSearchTemplateList_WithExtendAttribute() throws MetadataServiceException {
        // 准备测试数据
        String apiName = "GoalValueObj";
        String extendAttribute = "test_extend_attribute";

        // 执行测试
        List<ISearchTemplate> result = goalValueInitSceneProvider.getDefaultSearchTemplateList(testUser, apiName, extendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTemplates, result);
        
        // 验证调用了服务方法（扩展属性参数在此实现中未使用，但方法应正常执行）
        verify(searchTemplateService).findByObjectDescribeAPINameAndCode(eq("74255"), eq("GoalValueObj"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(goalValueInitSceneProvider);
    }
}
