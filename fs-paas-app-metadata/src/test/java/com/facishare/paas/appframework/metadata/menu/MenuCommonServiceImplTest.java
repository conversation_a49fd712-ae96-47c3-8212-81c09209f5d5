package com.facishare.paas.appframework.metadata.menu;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MenuCommonServiceImpl的单元测试
 * 测试菜单通用服务的功能
 */
@ExtendWith(MockitoExtension.class)
class MenuCommonServiceImplTest {

    @Mock
    private DescribeLogicService mockDescribeLogicService;

    @Mock
    private MetaDataFindService mockMetaDataFindService;

    @Mock
    private MetaDataActionService mockMetaDataActionService;

    @Mock
    private User mockUser;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @Mock
    private QueryResult<IObjectData> mockQueryResult;

    @InjectMocks
    private MenuCommonServiceImpl menuCommonService;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
        lenient().when(mockDescribeLogicService.findObject(anyString(), anyString())).thenReturn(mockObjectDescribe);
        lenient().when(mockQueryResult.getData()).thenReturn(new ArrayList<>());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createMenuItem的正常场景
     */
    @Test
    @DisplayName("正常场景 - createMenuItem创建菜单项成功")
    void testCreateMenuItem_Success() {
        // 准备测试数据
        String apiName = "TestObject";
        
        // Mock默认CRM菜单
        IObjectData mockCrmMenu = new ObjectData();
        mockCrmMenu.setId("menuId123");
        
        // Mock查询结果 - 没有找到现有菜单项
        when(mockQueryResult.getData()).thenReturn(Collections.emptyList());
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            IObjectData result = menuCommonService.createMenuItem(mockUser, apiName);
            assertNotNull(result);
        });

        // 验证调用了保存方法
        verify(mockMetaDataActionService, atLeastOnce()).saveObjectData(eq(mockUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createMenuItem时菜单项已存在的场景
     */
    @Test
    @DisplayName("边界场景 - createMenuItem时菜单项已存在")
    void testCreateMenuItem_MenuItemExists() {
        // 准备测试数据
        String apiName = "TestObject";
        
        // Mock默认CRM菜单
        IObjectData mockCrmMenu = new ObjectData();
        mockCrmMenu.setId("menuId123");
        
        // Mock现有菜单项
        IObjectData existingMenuItem = new ObjectData();
        existingMenuItem.setId("existingMenuItemId");
        
        // Mock查询结果 - 找到现有菜单项
        List<IObjectData> existingMenuItems = new ArrayList<>();
        existingMenuItems.add(existingMenuItem);
        when(mockQueryResult.getData()).thenReturn(existingMenuItems);
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            IObjectData result = menuCommonService.createMenuItem(mockUser, apiName);
            assertNotNull(result);
            assertEquals(existingMenuItem, result);
        });

        // 验证没有调用保存方法
        verify(mockMetaDataActionService, never()).saveObjectData(any(User.class), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createOrUpdateMenuItemWithOrder的正常场景
     */
    @Test
    @DisplayName("正常场景 - createOrUpdateMenuItemWithOrder创建成功")
    void testCreateOrUpdateMenuItemWithOrder_Success() {
        // 准备测试数据
        String apiName = "TestObject";
        Integer order = 10;
        
        // Mock查询结果 - 没有找到现有菜单项
        when(mockQueryResult.getData()).thenReturn(Collections.emptyList());
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            IObjectData result = menuCommonService.createOrUpdateMenuItemWithOrder(mockUser, apiName, order);
            assertNotNull(result);
        });

        // 验证调用了保存方法
        verify(mockMetaDataActionService, atLeastOnce()).saveObjectData(eq(mockUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createOrUpdateMenuItemWithOrder时order为null的异常场景
     */
    @Test
    @DisplayName("异常场景 - createOrUpdateMenuItemWithOrder时order为null")
    void testCreateOrUpdateMenuItemWithOrder_NullOrder() {
        // 准备测试数据
        String apiName = "TestObject";
        Integer order = null;

        // 执行测试并验证异常
        assertThrows(ValidateException.class, () -> {
            menuCommonService.createOrUpdateMenuItemWithOrder(mockUser, apiName, order);
        });

        // 验证没有调用保存方法
        verify(mockMetaDataActionService, never()).saveObjectData(any(User.class), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findDefaultCrmMenu的正常场景
     */
    @Test
    @DisplayName("正常场景 - findDefaultCrmMenu找到默认菜单")
    void testFindDefaultCrmMenu_Success() {
        // 准备测试数据
        IObjectData mockCrmMenu = new ObjectData();
        mockCrmMenu.setId("defaultCrmMenuId");
        
        List<IObjectData> menuList = new ArrayList<>();
        menuList.add(mockCrmMenu);
        when(mockQueryResult.getData()).thenReturn(menuList);
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        IObjectData result = menuCommonService.findDefaultCrmMenu(mockUser);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockCrmMenu, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findDefaultCrmMenu时没有找到默认菜单的场景
     */
    @Test
    @DisplayName("边界场景 - findDefaultCrmMenu没有找到默认菜单")
    void testFindDefaultCrmMenu_NotFound() {
        // Mock查询结果 - 空列表
        when(mockQueryResult.getData()).thenReturn(Collections.emptyList());
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        IObjectData result = menuCommonService.findDefaultCrmMenu(mockUser);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findMenuItemByApiName的正常场景
     */
    @Test
    @DisplayName("正常场景 - findMenuItemByApiName找到菜单项")
    void testFindMenuItemByApiName_Success() {
        // 准备测试数据
        String menuId = "menuId123";
        List<String> apiNames = new ArrayList<>();
        apiNames.add("TestObject1");
        apiNames.add("TestObject2");
        
        IObjectData menuItem1 = new ObjectData();
        menuItem1.setId("menuItem1");
        IObjectData menuItem2 = new ObjectData();
        menuItem2.setId("menuItem2");
        
        List<IObjectData> menuItems = new ArrayList<>();
        menuItems.add(menuItem1);
        menuItems.add(menuItem2);
        
        when(mockQueryResult.getData()).thenReturn(menuItems);
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        List<IObjectData> result = menuCommonService.findMenuItemByApiName(mockUser, menuId, apiNames);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(menuItems, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findMenuItemByApiName时没有找到菜单项的场景
     */
    @Test
    @DisplayName("边界场景 - findMenuItemByApiName没有找到菜单项")
    void testFindMenuItemByApiName_NotFound() {
        // 准备测试数据
        String menuId = "menuId123";
        List<String> apiNames = new ArrayList<>();
        apiNames.add("NonExistentObject");
        
        when(mockQueryResult.getData()).thenReturn(Collections.emptyList());
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        List<IObjectData> result = menuCommonService.findMenuItemByApiName(mockUser, menuId, apiNames);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findLastCrmMenuItem的正常场景
     */
    @Test
    @DisplayName("正常场景 - findLastCrmMenuItem找到最后一个菜单项")
    void testFindLastCrmMenuItem_Success() {
        // 准备测试数据
        String menuId = "menuId123";
        
        IObjectData lastMenuItem = new ObjectData();
        lastMenuItem.setId("lastMenuItemId");
        
        List<IObjectData> menuItems = new ArrayList<>();
        menuItems.add(lastMenuItem);
        
        when(mockQueryResult.getData()).thenReturn(menuItems);
        when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), anyString(), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行测试
        IObjectData result = menuCommonService.findLastCrmMenuItem(mockUser, menuId);

        // 验证结果
        assertNotNull(result);
        assertEquals(lastMenuItem, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildMenuItem的正常场景
     */
    @Test
    @DisplayName("正常场景 - buildMenuItem构建菜单项成功")
    void testBuildMenuItem_Success() {
        // 准备测试数据
        String menuId = "menuId123";
        String objectApiName = "TestObject";

        // 执行测试
        IObjectData result = menuCommonService.buildMenuItem(mockUser, menuId, mockObjectDescribe, objectApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(menuId, result.get(MenuConstants.MenuItemField.MENUID.getApiName()));
        assertEquals(objectApiName, result.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理场景
     */
    @Test
    @DisplayName("异常场景 - 传入null参数")
    void testNullParameters() {
        // 测试传入null用户
        assertThrows(Exception.class, () -> {
            menuCommonService.createMenuItem(null, "TestObject");
        });

        // 测试传入null apiName
        assertDoesNotThrow(() -> {
            menuCommonService.createMenuItem(mockUser, null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(menuCommonService);
        assertNotNull(mockUser);
        assertNotNull(mockDescribeLogicService);
        assertNotNull(mockMetaDataFindService);
        assertNotNull(mockMetaDataActionService);
    }
}
