package com.facishare.paas.appframework.metadata.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * I18KeyBuildUtils工具类的单元测试
 * 测试国际化键值构建功能
 */
@ExtendWith(MockitoExtension.class)
class I18KeyBuildUtilsTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则变更提示键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建变更规则变更提示键成功")
    void testGetChangeRuleChangePromptKey_Success() {
        // 测试正常的规则API名称
        String ruleApiName = "testRule";
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        
        assertEquals("change_rule.testRule.change_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则变更提示键时，规则名称包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 构建变更规则变更提示键时规则名称包含特殊字符")
    void testGetChangeRuleChangePromptKey_SpecialCharacters() {
        // 测试包含特殊字符的规则API名称
        String ruleApiName = "test_rule-v1.0";
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        
        assertEquals("change_rule.test_rule-v1.0.change_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则变更提示键时，规则名称为空的场景
     */
    @Test
    @DisplayName("边界场景 - 构建变更规则变更提示键时规则名称为空")
    void testGetChangeRuleChangePromptKey_EmptyRuleName() {
        // 测试空字符串
        String emptyRuleApiName = "";
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(emptyRuleApiName);
        
        assertEquals("change_rule..change_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则变更提示键时，规则名称为null的场景
     */
    @Test
    @DisplayName("边界场景 - 构建变更规则变更提示键时规则名称为null")
    void testGetChangeRuleChangePromptKey_NullRuleName() {
        // 测试null值
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(null);
        
        assertEquals("change_rule.null.change_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则验证提示键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建变更规则验证提示键成功")
    void testGetChangeRuleVerificationPromptKey_Success() {
        // 测试正常的规则API名称
        String ruleApiName = "validationRule";
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        
        assertEquals("change_rule.validationRule.verification_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则验证提示键时，规则名称包含数字的场景
     */
    @Test
    @DisplayName("正常场景 - 构建变更规则验证提示键时规则名称包含数字")
    void testGetChangeRuleVerificationPromptKey_WithNumbers() {
        // 测试包含数字的规则API名称
        String ruleApiName = "rule123";
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        
        assertEquals("change_rule.rule123.verification_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建变更规则验证提示键时，规则名称为空的场景
     */
    @Test
    @DisplayName("边界场景 - 构建变更规则验证提示键时规则名称为空")
    void testGetChangeRuleVerificationPromptKey_EmptyRuleName() {
        // 测试空字符串
        String emptyRuleApiName = "";
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(emptyRuleApiName);
        
        assertEquals("change_rule..verification_prompt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则名称键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建重复规则名称键成功")
    void testGetDuplicatedRulesNameKey_Success() {
        // 测试正常的对象API名称和规则名称
        String objectApiName = "Account";
        String ruleName = "uniqueRule";
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        
        assertEquals("repeat_rule.Account.uniqueRule.name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则名称键时，参数包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 构建重复规则名称键时参数包含特殊字符")
    void testGetDuplicatedRulesNameKey_SpecialCharacters() {
        // 测试包含特殊字符的参数
        String objectApiName = "Custom_Object__c";
        String ruleName = "unique-rule_v2";
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        
        assertEquals("repeat_rule.Custom_Object__c.unique-rule_v2.name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则名称键时，参数为空的场景
     */
    @Test
    @DisplayName("边界场景 - 构建重复规则名称键时参数为空")
    void testGetDuplicatedRulesNameKey_EmptyParameters() {
        // 测试空字符串参数
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey("", "");
        
        assertEquals("repeat_rule...name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则名称键时，参数为null的场景
     */
    @Test
    @DisplayName("边界场景 - 构建重复规则名称键时参数为null")
    void testGetDuplicatedRulesNameKey_NullParameters() {
        // 测试null参数
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(null, null);
        
        assertEquals("repeat_rule.null.null.name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则描述键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建重复规则描述键成功")
    void testGetDuplicatedRulesDescriptionKey_Success() {
        // 测试正常的对象API名称和规则名称
        String objectApiName = "Contact";
        String ruleName = "emailUniqueRule";
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);
        
        assertEquals("repeat_rule.Contact.emailUniqueRule.description", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则描述键时，参数包含中文的场景
     */
    @Test
    @DisplayName("正常场景 - 构建重复规则描述键时参数包含中文")
    void testGetDuplicatedRulesDescriptionKey_ChineseCharacters() {
        // 测试包含中文的参数（虽然实际业务中可能不会这样使用）
        String objectApiName = "客户";
        String ruleName = "唯一规则";
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);
        
        assertEquals("repeat_rule.客户.唯一规则.description", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则描述键时，参数为空的场景
     */
    @Test
    @DisplayName("边界场景 - 构建重复规则描述键时参数为空")
    void testGetDuplicatedRulesDescriptionKey_EmptyParameters() {
        // 测试空字符串参数
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey("", "");
        
        assertEquals("repeat_rule...description", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建重复规则描述键时，只有一个参数为空的场景
     */
    @Test
    @DisplayName("边界场景 - 构建重复规则描述键时只有一个参数为空")
    void testGetDuplicatedRulesDescriptionKey_OneEmptyParameter() {
        // 测试一个参数为空的情况
        String result1 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey("Object", "");
        assertEquals("repeat_rule.Object..description", result1);
        
        String result2 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey("", "rule");
        assertEquals("repeat_rule..rule.description", result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有方法的键格式一致性
     */
    @Test
    @DisplayName("一致性验证 - 验证所有方法的键格式一致性")
    void testKeyFormatConsistency() {
        String objectApiName = "TestObject";
        String ruleName = "testRule";
        String ruleApiName = "testRule";
        
        // 验证所有方法都使用点号分隔符
        String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        String nameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        String descriptionKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);
        
        // 验证所有键都包含点号分隔符
        assertTrue(changePromptKey.contains("."));
        assertTrue(verificationPromptKey.contains("."));
        assertTrue(nameKey.contains("."));
        assertTrue(descriptionKey.contains("."));
        
        // 验证键的结构
        assertTrue(changePromptKey.startsWith("change_rule."));
        assertTrue(verificationPromptKey.startsWith("change_rule."));
        assertTrue(nameKey.startsWith("repeat_rule."));
        assertTrue(descriptionKey.startsWith("repeat_rule."));
        
        assertTrue(changePromptKey.endsWith(".change_prompt"));
        assertTrue(verificationPromptKey.endsWith(".verification_prompt"));
        assertTrue(nameKey.endsWith(".name"));
        assertTrue(descriptionKey.endsWith(".description"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法返回值的不可变性
     */
    @Test
    @DisplayName("不可变性验证 - 验证相同输入产生相同输出")
    void testMethodImmutability() {
        String ruleApiName = "testRule";
        String objectApiName = "TestObject";
        String ruleName = "testRule";
        
        // 多次调用相同方法，验证返回值一致
        String result1 = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        String result2 = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        assertEquals(result1, result2);
        
        String result3 = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        String result4 = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        assertEquals(result3, result4);
        
        String result5 = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        String result6 = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        assertEquals(result5, result6);
        
        String result7 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);
        String result8 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);
        assertEquals(result7, result8);
    }
}
