package com.facishare.paas.appframework.metadata.initscene;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SceneInitManager的单元测试
 * 测试场景初始化管理器的功能
 */
@ExtendWith(MockitoExtension.class)
class SceneInitManagerTest {

    @Mock
    private DefaultSceneProvider mockDefaultSceneProvider;

    @Mock
    private ApplicationContext mockApplicationContext;

    @Mock
    private SceneInitProvider mockCustomProvider1;

    @Mock
    private SceneInitProvider mockCustomProvider2;

    @InjectMocks
    private SceneInitManager sceneInitManager;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockCustomProvider1.getApiName()).thenReturn("Account");
        lenient().when(mockCustomProvider2.getApiName()).thenReturn("Contact");
        lenient().when(mockDefaultSceneProvider.getApiName()).thenReturn(null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext的正常场景
     */
    @Test
    @DisplayName("正常场景 - setApplicationContext初始化Provider映射")
    void testSetApplicationContext_Success() {
        // 准备测试数据
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        springBeanMap.put("customProvider2", mockCustomProvider2);
        springBeanMap.put("defaultProvider", mockDefaultSceneProvider);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        // 执行测试
        assertDoesNotThrow(() -> {
            sceneInitManager.setApplicationContext(mockApplicationContext);
        });

        // 验证Provider映射是否正确建立
        SceneInitProvider accountProvider = sceneInitManager.getProvider("Account");
        SceneInitProvider contactProvider = sceneInitManager.getProvider("Contact");

        assertNotNull(accountProvider);
        assertNotNull(contactProvider);
        assertEquals(mockCustomProvider1, accountProvider);
        assertEquals(mockCustomProvider2, contactProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext时没有自定义Provider的场景
     */
    @Test
    @DisplayName("边界场景 - setApplicationContext时只有默认Provider")
    void testSetApplicationContext_OnlyDefaultProvider() {
        // 准备测试数据 - 只有默认Provider
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("defaultProvider", mockDefaultSceneProvider);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        // 执行测试
        assertDoesNotThrow(() -> {
            sceneInitManager.setApplicationContext(mockApplicationContext);
        });

        // 验证获取不存在的Provider时返回默认Provider
        SceneInitProvider unknownProvider = sceneInitManager.getProvider("UnknownObject");
        assertEquals(mockDefaultSceneProvider, unknownProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext时Provider映射为空的场景
     */
    @Test
    @DisplayName("边界场景 - setApplicationContext时Provider映射为空")
    void testSetApplicationContext_EmptyProviderMap() {
        // 准备测试数据 - 空的Provider映射
        Map<String, SceneInitProvider> emptySpringBeanMap = new HashMap<>();

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(emptySpringBeanMap);

        // 执行测试
        assertDoesNotThrow(() -> {
            sceneInitManager.setApplicationContext(mockApplicationContext);
        });

        // 验证获取任何Provider时都返回默认Provider
        SceneInitProvider anyProvider = sceneInitManager.getProvider("AnyObject");
        assertEquals(mockDefaultSceneProvider, anyProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider的正常场景
     */
    @Test
    @DisplayName("正常场景 - getProvider获取已注册的Provider")
    void testGetProvider_RegisteredProvider() {
        // 先初始化Provider映射
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 执行测试
        SceneInitProvider result = sceneInitManager.getProvider("Account");

        // 验证结果
        assertNotNull(result);
        assertEquals(mockCustomProvider1, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider时请求未注册的Provider
     */
    @Test
    @DisplayName("边界场景 - getProvider获取未注册的Provider返回默认Provider")
    void testGetProvider_UnregisteredProvider() {
        // 先初始化Provider映射
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 执行测试 - 请求未注册的Provider
        SceneInitProvider result = sceneInitManager.getProvider("UnregisteredObject");

        // 验证结果 - 应该返回默认Provider
        assertNotNull(result);
        assertEquals(mockDefaultSceneProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider时传入null参数
     */
    @Test
    @DisplayName("边界场景 - getProvider传入null参数")
    void testGetProvider_NullApiName() {
        // 先初始化Provider映射
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 执行测试 - 传入null参数
        SceneInitProvider result = sceneInitManager.getProvider(null);

        // 验证结果 - 应该返回默认Provider
        assertNotNull(result);
        assertEquals(mockDefaultSceneProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider时传入空字符串参数
     */
    @Test
    @DisplayName("边界场景 - getProvider传入空字符串参数")
    void testGetProvider_EmptyApiName() {
        // 先初始化Provider映射
        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 执行测试 - 传入空字符串
        SceneInitProvider result = sceneInitManager.getProvider("");

        // 验证结果 - 应该返回默认Provider
        assertNotNull(result);
        assertEquals(mockDefaultSceneProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Provider的ApiName为空或null时的处理
     */
    @Test
    @DisplayName("边界场景 - Provider的ApiName为空时不被注册")
    void testSetApplicationContext_ProviderWithEmptyApiName() {
        // 准备测试数据 - 包含ApiName为空的Provider
        SceneInitProvider mockEmptyApiNameProvider = mock(SceneInitProvider.class);
        when(mockEmptyApiNameProvider.getApiName()).thenReturn("");

        SceneInitProvider mockNullApiNameProvider = mock(SceneInitProvider.class);
        when(mockNullApiNameProvider.getApiName()).thenReturn(null);

        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        springBeanMap.put("emptyApiNameProvider", mockEmptyApiNameProvider);
        springBeanMap.put("nullApiNameProvider", mockNullApiNameProvider);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        // 执行测试
        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 验证只有有效ApiName的Provider被注册
        SceneInitProvider accountProvider = sceneInitManager.getProvider("Account");
        SceneInitProvider emptyProvider = sceneInitManager.getProvider("");
        SceneInitProvider nullProvider = sceneInitManager.getProvider(null);

        assertEquals(mockCustomProvider1, accountProvider);
        assertEquals(mockDefaultSceneProvider, emptyProvider);
        assertEquals(mockDefaultSceneProvider, nullProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂场景 - 多个Provider注册和获取
     */
    @Test
    @DisplayName("复杂场景 - 多个Provider注册和获取")
    void testComplexScenario_MultipleProvidersRegistrationAndRetrieval() {
        // 准备测试数据 - 多个Provider
        SceneInitProvider mockProvider3 = mock(SceneInitProvider.class);
        when(mockProvider3.getApiName()).thenReturn("Opportunity");

        Map<String, SceneInitProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("provider1", mockCustomProvider1);
        springBeanMap.put("provider2", mockCustomProvider2);
        springBeanMap.put("provider3", mockProvider3);
        springBeanMap.put("defaultProvider", mockDefaultSceneProvider);

        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenReturn(springBeanMap);

        // 执行初始化
        sceneInitManager.setApplicationContext(mockApplicationContext);

        // 验证所有Provider都能正确获取
        assertEquals(mockCustomProvider1, sceneInitManager.getProvider("Account"));
        assertEquals(mockCustomProvider2, sceneInitManager.getProvider("Contact"));
        assertEquals(mockProvider3, sceneInitManager.getProvider("Opportunity"));
        assertEquals(mockDefaultSceneProvider, sceneInitManager.getProvider("UnknownObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理场景
     */
    @Test
    @DisplayName("异常场景 - ApplicationContext抛出异常")
    void testSetApplicationContext_Exception() {
        // Mock ApplicationContext抛出异常
        when(mockApplicationContext.getBeansOfType(SceneInitProvider.class))
                .thenThrow(new RuntimeException("ApplicationContext error"));

        // 执行测试 - 应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            sceneInitManager.setApplicationContext(mockApplicationContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(sceneInitManager);
        assertNotNull(mockDefaultSceneProvider);
        assertNotNull(mockApplicationContext);
    }
}
