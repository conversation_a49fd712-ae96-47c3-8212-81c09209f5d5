package com.facishare.paas.appframework.metadata.mtresource;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MtResourceServiceImpl的单元测试
 * 测试多租户资源服务的功能
 */
@ExtendWith(MockitoExtension.class)
class MtResourceServiceImplTest {

    @Mock
    private IRepository<MtResource> mockRepository;

    @Mock
    private DescribeLogicService mockDescribeLogicService;

    @Mock
    private User mockUser;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @InjectMocks
    private MtResourceServiceImpl mtResourceService;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryResource的正常场景
     */
    @Test
    @DisplayName("正常场景 - queryResource查询资源成功")
    void testQueryResource_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> resourceValues = Arrays.asList("value1", "value2");

        // Mock返回结果
        List<MtResource> mockResources = new ArrayList<>();
        MtResource resource1 = new MtResource();
        resource1.setResourceValue("value1");
        MtResource resource2 = new MtResource();
        resource2.setResourceValue("value2");
        mockResources.add(resource1);
        mockResources.add(resource2);

        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(mockResources);

        // 执行测试
        List<MtResource> result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("value1", result.get(0).getResourceValue());
        assertEquals("value2", result.get(1).getResourceValue());

        // 验证调用了repository
        verify(mockRepository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryResource时资源值列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - queryResource时资源值列表为空")
    void testQueryResource_EmptyResourceValues() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> emptyResourceValues = Collections.emptyList();

        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<MtResource> result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, emptyResourceValues);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryResource带源类型和源值的重载方法
     */
    @Test
    @DisplayName("正常场景 - queryResource带源类型和源值")
    void testQueryResourceWithSourceTypeAndValue_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> resourceValues = Arrays.asList("value1");
        String sourceType = "USER";
        String sourceValue = "userId123";

        List<MtResource> mockResources = new ArrayList<>();
        MtResource resource = new MtResource();
        resource.setResourceValue("value1");
        resource.setSourceType(sourceType);
        resource.setSourceValue(sourceValue);
        mockResources.add(resource);

        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(mockResources);

        // 执行测试
        List<MtResource> result = mtResourceService.queryResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(sourceType, result.get(0).getSourceType());
        assertEquals(sourceValue, result.get(0).getSourceValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryResourceByResourceTypes的正常场景
     */
    @Test
    @DisplayName("正常场景 - queryResourceByResourceTypes按资源类型查询")
    void testQueryResourceByResourceTypes_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        List<String> resourceTypes = Arrays.asList("OBJECT", "FIELD");
        String resourceValue = "testValue";

        List<MtResource> mockResources = new ArrayList<>();
        MtResource resource1 = new MtResource();
        resource1.setResourceType("OBJECT");
        MtResource resource2 = new MtResource();
        resource2.setResourceType("FIELD");
        mockResources.add(resource1);
        mockResources.add(resource2);

        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(mockResources);

        // 执行测试
        List<MtResource> result = mtResourceService.queryResourceByResourceTypes(tenantId, resourceParentValue, resourceTypes, resourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试modifyResource的正常场景
     */
    @Test
    @DisplayName("正常场景 - modifyResource修改资源成功")
    void testModifyResource_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> resourceValues = Arrays.asList("value1", "value2");
        String controlLevel = "READ";
        String sourceType = "USER";
        String sourceValue = "userId123";

        // Mock查询现有资源
        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(Collections.emptyList());

        // Mock批量更新
        when(mockRepository.bulkUpsert(any(User.class), anyList())).thenReturn(Lists.newArrayList());

        // 执行测试
        List<MtResource> result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, resourceValues, controlLevel, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证调用了批量更新
        verify(mockRepository).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试modifyResource时资源值列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - modifyResource时资源值列表为空")
    void testModifyResource_EmptyResourceValues() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> emptyResourceValues = Collections.emptyList();
        String controlLevel = "READ";
        String sourceType = "USER";
        String sourceValue = "userId123";

        // 执行测试
        List<MtResource> result = mtResourceService.modifyResource(tenantId, resourceParentValue, resourceType, emptyResourceValues, controlLevel, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用批量更新
        verify(mockRepository, never()).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableResource的正常场景
     */
    @Test
    @DisplayName("正常场景 - disableResource禁用资源成功")
    void testDisableResource_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> resourceValues = Arrays.asList("value1");
        String sourceType = "USER";

        // Mock查询现有资源
        List<MtResource> existingResources = new ArrayList<>();
        MtResource resource = new MtResource();
        resource.setResourceValue("value1");
        resource.setStatus(MtResource.STATUS_TYPE_ENABLE);
        existingResources.add(resource);

        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(existingResources);

        // 执行测试
        assertDoesNotThrow(() -> {
            mtResourceService.disableResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType);
        });

        // 验证调用了批量更新
        verify(mockRepository).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableResource时没有找到资源的场景
     */
    @Test
    @DisplayName("边界场景 - disableResource时没有找到资源")
    void testDisableResource_NoResourcesFound() {
        // 准备测试数据
        String tenantId = "testTenant";
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        List<String> resourceValues = Arrays.asList("nonExistentValue");
        String sourceType = "USER";

        // Mock查询结果为空
        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> {
            mtResourceService.disableResource(tenantId, resourceParentValue, resourceType, resourceValues, sourceType);
        });

        // 验证没有调用批量更新
        verify(mockRepository, never()).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateDescribeAndModifyResource的正常场景
     */
    @Test
    @DisplayName("正常场景 - updateDescribeAndModifyResource更新描述和修改资源")
    void testUpdateDescribeAndModifyResource_Success() {
        // 准备测试数据
        String tenantId = "testTenant";
        List<String> resourceValues = Arrays.asList("value1", "value2");
        String sourceType = "USER";
        String sourceValue = "userId123";
        String controlLevel = "READ";

        // Mock描述更新
        doNothing().when(mockDescribeLogicService).update(mockObjectDescribe);

        // Mock资源查询和更新
        when(mockRepository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(Collections.emptyList());
        when(mockRepository.bulkUpsert(any(User.class), anyList())).thenReturn(Lists.newArrayList());

        // 执行测试
        assertDoesNotThrow(() -> {
            mtResourceService.updateDescribeAndModifyResource(tenantId, mockObjectDescribe, resourceValues, sourceType, sourceValue, controlLevel);
        });

        // 验证调用了描述更新
        verify(mockDescribeLogicService).update(mockObjectDescribe);
        // 验证调用了资源更新
        verify(mockRepository).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试countByResourceType的正常场景
     */
    @Test
    @DisplayName("正常场景 - countByResourceType统计资源数量")
    void testCountByResourceType_Success() {
        // 准备测试数据
        String resourceParentValue = "parentValue";
        String resourceType = "OBJECT";
        Integer expectedCount = 5;

        when(mockRepository.findCountOnly(eq(mockUser), any(Query.class), eq(MtResource.class)))
                .thenReturn(expectedCount);

        // 执行测试
        Integer result = mtResourceService.countByResourceType(mockUser, resourceParentValue, resourceType);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCount, result);

        // 验证调用了计数方法
        verify(mockRepository).findCountOnly(eq(mockUser), any(Query.class), eq(MtResource.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理场景
     */
    @Test
    @DisplayName("异常场景 - 传入null参数")
    void testNullParameters() {
        // 测试传入null tenantId
        assertDoesNotThrow(() -> {
            mtResourceService.queryResource(null, "parentValue", "OBJECT", Arrays.asList("value1"));
        });

        // 测试传入null resourceValues
        assertDoesNotThrow(() -> {
            mtResourceService.queryResource("testTenant", "parentValue", "OBJECT", null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(mtResourceService);
        assertNotNull(mockRepository);
        assertNotNull(mockDescribeLogicService);
        assertNotNull(mockUser);
        assertNotNull(mockObjectDescribe);
    }
}
