package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * FilterButtonsManager的单元测试
 * 测试过滤按钮管理器的功能
 */
@ExtendWith(MockitoExtension.class)
class FilterButtonsManagerTest {

    @Mock
    private DefaultFilterButtonsProvider mockDefaultProvider;

    @Mock
    private FilterButtonsProviderProxy mockProviderProxy;

    @Mock
    private ApplicationContext mockApplicationContext;

    @Mock
    private FilterButtonsProvider mockCustomProvider1;

    @Mock
    private FilterButtonsProvider mockCustomProvider2;

    @InjectMocks
    private FilterButtonsManager filterButtonsManager;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockCustomProvider1.getDescribeApiName()).thenReturn("Account");
        lenient().when(mockCustomProvider2.getDescribeApiName()).thenReturn("Contact");
        lenient().when(mockDefaultProvider.getDescribeApiName()).thenReturn(null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext的正常场景
     */
    @Test
    @DisplayName("正常场景 - setApplicationContext初始化Provider映射")
    void testSetApplicationContext_Success() {
        // 准备测试数据
        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        springBeanMap.put("customProvider2", mockCustomProvider2);
        springBeanMap.put("defaultProvider", mockDefaultProvider);

        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);

        // 执行测试
        assertDoesNotThrow(() -> {
            filterButtonsManager.setApplicationContext(mockApplicationContext);
        });

        // 验证Provider映射是否正确建立
        FilterButtonsProvider accountProvider = filterButtonsManager.getLocalProvider("Account");
        FilterButtonsProvider contactProvider = filterButtonsManager.getLocalProvider("Contact");

        assertNotNull(accountProvider);
        assertNotNull(contactProvider);
        assertEquals(mockCustomProvider1, accountProvider);
        assertEquals(mockCustomProvider2, contactProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext时Provider的ApiName为空的场景
     */
    @Test
    @DisplayName("边界场景 - setApplicationContext时Provider的ApiName为空")
    void testSetApplicationContext_EmptyApiName() {
        // 准备测试数据 - 包含ApiName为空的Provider
        FilterButtonsProvider mockEmptyApiNameProvider = mock(FilterButtonsProvider.class);
        when(mockEmptyApiNameProvider.getDescribeApiName()).thenReturn("");

        FilterButtonsProvider mockNullApiNameProvider = mock(FilterButtonsProvider.class);
        when(mockNullApiNameProvider.getDescribeApiName()).thenReturn(null);

        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        springBeanMap.put("emptyApiNameProvider", mockEmptyApiNameProvider);
        springBeanMap.put("nullApiNameProvider", mockNullApiNameProvider);

        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);

        // 执行测试
        filterButtonsManager.setApplicationContext(mockApplicationContext);

        // 验证只有有效ApiName的Provider被注册
        FilterButtonsProvider accountProvider = filterButtonsManager.getLocalProvider("Account");
        FilterButtonsProvider unknownProvider = filterButtonsManager.getLocalProvider("UnknownObject");

        assertEquals(mockCustomProvider1, accountProvider);
        assertEquals(mockDefaultProvider, unknownProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider的正常场景 - 自定义对象
     */
    @Test
    @DisplayName("正常场景 - getProvider获取自定义对象Provider")
    void testGetProvider_CustomObject() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            // Mock自定义对象检查
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("CustomObject__c"))
                    .thenReturn(true);

            // 执行测试
            FilterButtonsProvider result = filterButtonsManager.getProvider("testTenant", "CustomObject__c");

            // 验证结果 - 自定义对象应该返回默认Provider
            assertNotNull(result);
            assertEquals(mockDefaultProvider, result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider的正常场景 - 标准对象且灰度开启
     */
    @Test
    @DisplayName("正常场景 - getProvider获取标准对象Provider且灰度开启")
    void testGetProvider_StandardObjectWithGrayEnabled() {
        // 先初始化Provider映射
        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);
        filterButtonsManager.setApplicationContext(mockApplicationContext);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // Mock标准对象检查
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("Account"))
                    .thenReturn(false);

            // Mock灰度配置开启
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isFilterButtonsProviderGray("testTenant", "Account"))
                    .thenReturn(true);

            // 执行测试
            FilterButtonsProvider result = filterButtonsManager.getProvider("testTenant", "Account");

            // 验证结果 - 应该返回注册的Provider
            assertNotNull(result);
            assertEquals(mockCustomProvider1, result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider的场景 - 标准对象但灰度关闭
     */
    @Test
    @DisplayName("边界场景 - getProvider获取标准对象Provider但灰度关闭")
    void testGetProvider_StandardObjectWithGrayDisabled() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // Mock标准对象检查
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("Account"))
                    .thenReturn(false);

            // Mock灰度配置关闭
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isFilterButtonsProviderGray("testTenant", "Account"))
                    .thenReturn(false);

            // 执行测试
            FilterButtonsProvider result = filterButtonsManager.getProvider("testTenant", "Account");

            // 验证结果 - 灰度关闭时应该返回默认Provider
            assertNotNull(result);
            assertEquals(mockDefaultProvider, result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider的场景 - 未注册的对象返回远程Provider
     */
    @Test
    @DisplayName("边界场景 - getProvider获取未注册对象返回远程Provider")
    void testGetProvider_UnregisteredObjectReturnsRemoteProvider() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // Mock标准对象检查
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("UnknownObject"))
                    .thenReturn(false);

            // Mock灰度配置开启
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isFilterButtonsProviderGray("testTenant", "UnknownObject"))
                    .thenReturn(true);

            // 执行测试
            FilterButtonsProvider result = filterButtonsManager.getProvider("testTenant", "UnknownObject");

            // 验证结果 - 应该返回远程Provider
            assertNotNull(result);
            assertTrue(result instanceof RemoteFilterButtonsProvider);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider的正常场景
     */
    @Test
    @DisplayName("正常场景 - getLocalProvider获取本地Provider")
    void testGetLocalProvider_Success() {
        // 先初始化Provider映射
        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);
        filterButtonsManager.setApplicationContext(mockApplicationContext);

        // 执行测试
        FilterButtonsProvider result = filterButtonsManager.getLocalProvider("Account");

        // 验证结果
        assertNotNull(result);
        assertEquals(mockCustomProvider1, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider的边界场景 - 未注册的对象
     */
    @Test
    @DisplayName("边界场景 - getLocalProvider获取未注册对象返回默认Provider")
    void testGetLocalProvider_UnregisteredObject() {
        // 先初始化Provider映射
        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("customProvider1", mockCustomProvider1);
        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);
        filterButtonsManager.setApplicationContext(mockApplicationContext);

        // 执行测试
        FilterButtonsProvider result = filterButtonsManager.getLocalProvider("UnknownObject");

        // 验证结果 - 应该返回默认Provider
        assertNotNull(result);
        assertEquals(mockDefaultProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRemoteProvider的正常场景
     */
    @Test
    @DisplayName("正常场景 - getRemoteProvider创建远程Provider")
    void testGetRemoteProvider_Success() {
        // 执行测试
        FilterButtonsProvider result = filterButtonsManager.getRemoteProvider("RemoteObject");

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof RemoteFilterButtonsProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂场景 - 多个Provider注册和获取
     */
    @Test
    @DisplayName("复杂场景 - 多个Provider注册和获取")
    void testComplexScenario_MultipleProvidersRegistrationAndRetrieval() {
        // 准备测试数据 - 多个Provider
        FilterButtonsProvider mockProvider3 = mock(FilterButtonsProvider.class);
        when(mockProvider3.getDescribeApiName()).thenReturn("Opportunity");

        Map<String, FilterButtonsProvider> springBeanMap = new HashMap<>();
        springBeanMap.put("provider1", mockCustomProvider1);
        springBeanMap.put("provider2", mockCustomProvider2);
        springBeanMap.put("provider3", mockProvider3);
        springBeanMap.put("defaultProvider", mockDefaultProvider);

        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenReturn(springBeanMap);

        // 执行初始化
        filterButtonsManager.setApplicationContext(mockApplicationContext);

        // 验证所有Provider都能正确获取
        assertEquals(mockCustomProvider1, filterButtonsManager.getLocalProvider("Account"));
        assertEquals(mockCustomProvider2, filterButtonsManager.getLocalProvider("Contact"));
        assertEquals(mockProvider3, filterButtonsManager.getLocalProvider("Opportunity"));
        assertEquals(mockDefaultProvider, filterButtonsManager.getLocalProvider("UnknownObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常处理场景
     */
    @Test
    @DisplayName("异常场景 - ApplicationContext抛出异常")
    void testSetApplicationContext_Exception() {
        // Mock ApplicationContext抛出异常
        when(mockApplicationContext.getBeansOfType(FilterButtonsProvider.class))
                .thenThrow(new RuntimeException("ApplicationContext error"));

        // 执行测试 - 应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            filterButtonsManager.setApplicationContext(mockApplicationContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null参数处理
     */
    @Test
    @DisplayName("边界场景 - 处理null参数")
    void testNullParameters() {
        // 测试getLocalProvider传入null
        FilterButtonsProvider result1 = filterButtonsManager.getLocalProvider(null);
        assertEquals(mockDefaultProvider, result1);

        // 测试getRemoteProvider传入null
        FilterButtonsProvider result2 = filterButtonsManager.getRemoteProvider(null);
        assertNotNull(result2);
        assertTrue(result2 instanceof RemoteFilterButtonsProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(filterButtonsManager);
        assertNotNull(mockDefaultProvider);
        assertNotNull(mockProviderProxy);
        assertNotNull(mockApplicationContext);
    }
}
