package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DefaultFilterButtonsProvider的单元测试
 * 测试默认过滤按钮提供器的功能
 */
@ExtendWith(MockitoExtension.class)
class DefaultFilterButtonsProviderTest {

    @Mock
    private User mockUser;

    @InjectMocks
    private DefaultFilterButtonsProvider defaultFilterButtonsProvider;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(mockUser.getTenantId()).thenReturn("testTenant");
        lenient().when(mockUser.getUserId()).thenReturn("testUserId");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDescribeApiName方法
     */
    @Test
    @DisplayName("正常场景 - getDescribeApiName返回null")
    void testGetDescribeApiName_ReturnsNull() {
        // 执行测试
        String result = defaultFilterButtonsProvider.getDescribeApiName();

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons的正常场景 - 返回原始按钮列表
     */
    @Test
    @DisplayName("正常场景 - getButtons返回原始按钮列表")
    void testGetButtons_ReturnsOriginalButtons() {
        // 准备测试数据
        List<IUdefButton> originalButtons = new ArrayList<>();
        
        IUdefButton button1 = new UdefButton();
        button1.setApiName("button1");
        button1.setLabel("按钮1");
        
        IUdefButton button2 = new UdefButton();
        button2.setApiName("button2");
        button2.setLabel("按钮2");
        
        originalButtons.add(button1);
        originalButtons.add(button2);

        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, originalButtons);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(originalButtons, result);
        assertSame(originalButtons, result); // 验证返回的是同一个对象引用
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons时传入空列表
     */
    @Test
    @DisplayName("边界场景 - getButtons传入空列表")
    void testGetButtons_EmptyButtonsList() {
        // 准备测试数据
        List<IUdefButton> emptyButtons = Collections.emptyList();

        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, emptyButtons);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertEquals(emptyButtons, result);
        assertSame(emptyButtons, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons时传入null按钮列表
     */
    @Test
    @DisplayName("边界场景 - getButtons传入null按钮列表")
    void testGetButtons_NullButtonsList() {
        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, null);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons时传入null用户
     */
    @Test
    @DisplayName("边界场景 - getButtons传入null用户")
    void testGetButtons_NullUser() {
        // 准备测试数据
        List<IUdefButton> buttons = new ArrayList<>();
        IUdefButton button = new UdefButton();
        button.setApiName("testButton");
        buttons.add(button);

        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(null, buttons);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(buttons, result);
        assertSame(buttons, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons时传入大量按钮
     */
    @Test
    @DisplayName("复杂场景 - getButtons处理大量按钮")
    void testGetButtons_LargeButtonsList() {
        // 准备测试数据 - 创建大量按钮
        List<IUdefButton> largeButtonsList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            IUdefButton button = new UdefButton();
            button.setApiName("button" + i);
            button.setLabel("按钮" + i);
            largeButtonsList.add(button);
        }

        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, largeButtonsList);

        // 验证结果
        assertNotNull(result);
        assertEquals(100, result.size());
        assertEquals(largeButtonsList, result);
        assertSame(largeButtonsList, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtons的性能
     */
    @Test
    @DisplayName("性能场景 - getButtons执行性能")
    void testGetButtons_Performance() {
        // 准备测试数据
        List<IUdefButton> buttons = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            IUdefButton button = new UdefButton();
            button.setApiName("button" + i);
            buttons.add(button);
        }

        // 测试执行时间
        long startTime = System.currentTimeMillis();
        
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, buttons);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证结果和性能
        assertNotNull(result);
        assertEquals(50, result.size());
        assertTrue(executionTime < 10, "方法执行时间过长: " + executionTime + "ms");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用的一致性
     */
    @Test
    @DisplayName("一致性测试 - 多次调用getButtons结果一致")
    void testGetButtons_Consistency() {
        // 准备测试数据
        List<IUdefButton> buttons = new ArrayList<>();
        IUdefButton button = new UdefButton();
        button.setApiName("testButton");
        buttons.add(button);

        // 多次调用
        List<IUdefButton> result1 = defaultFilterButtonsProvider.getButtons(mockUser, buttons);
        List<IUdefButton> result2 = defaultFilterButtonsProvider.getButtons(mockUser, buttons);
        List<IUdefButton> result3 = defaultFilterButtonsProvider.getButtons(mockUser, buttons);

        // 验证结果一致性
        assertEquals(result1, result2);
        assertEquals(result2, result3);
        assertSame(buttons, result1);
        assertSame(buttons, result2);
        assertSame(buttons, result3);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试按钮列表包含null元素的场景
     */
    @Test
    @DisplayName("边界场景 - getButtons处理包含null元素的按钮列表")
    void testGetButtons_ButtonsListWithNullElements() {
        // 准备测试数据 - 包含null元素的列表
        List<IUdefButton> buttonsWithNull = new ArrayList<>();
        buttonsWithNull.add(null);
        
        IUdefButton validButton = new UdefButton();
        validButton.setApiName("validButton");
        buttonsWithNull.add(validButton);
        
        buttonsWithNull.add(null);

        // 执行测试
        List<IUdefButton> result = defaultFilterButtonsProvider.getButtons(mockUser, buttonsWithNull);

        // 验证结果 - 默认实现应该原样返回，包括null元素
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(buttonsWithNull, result);
        assertSame(buttonsWithNull, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试接口实现的正确性
     */
    @Test
    @DisplayName("接口实现测试 - 验证FilterButtonsProvider接口实现")
    void testInterfaceImplementation() {
        // 验证实现了正确的接口
        assertTrue(defaultFilterButtonsProvider instanceof FilterButtonsProvider);
        
        // 验证接口方法存在
        assertDoesNotThrow(() -> {
            defaultFilterButtonsProvider.getDescribeApiName();
            defaultFilterButtonsProvider.getButtons(mockUser, new ArrayList<>());
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试类的基本属性
     */
    @Test
    @DisplayName("基本属性测试 - 验证类的基本特性")
    void testBasicProperties() {
        // 验证类不为null
        assertNotNull(defaultFilterButtonsProvider);
        
        // 验证类名
        assertEquals("DefaultFilterButtonsProvider", defaultFilterButtonsProvider.getClass().getSimpleName());
        
        // 验证包名
        assertEquals("com.facishare.paas.appframework.metadata.button", 
                    defaultFilterButtonsProvider.getClass().getPackage().getName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试注解配置
     */
    @Test
    @DisplayName("注解测试 - 验证@Component注解")
    void testAnnotations() {
        // 验证@Component注解
        boolean hasComponentAnnotation = defaultFilterButtonsProvider.getClass().isAnnotationPresent(
                org.springframework.stereotype.Component.class);
        assertTrue(hasComponentAnnotation, "应该有@Component注解");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试健壮性
     */
    @Test
    @DisplayName("健壮性测试 - 各种边界条件")
    void testRobustness() {
        // 测试所有参数都为null
        List<IUdefButton> result1 = defaultFilterButtonsProvider.getButtons(null, null);
        assertNull(result1);

        // 测试多次调用getDescribeApiName的稳定性
        for (int i = 0; i < 5; i++) {
            String apiName = defaultFilterButtonsProvider.getDescribeApiName();
            assertNull(apiName);
        }

        // 测试并发调用的安全性
        assertDoesNotThrow(() -> {
            Thread thread1 = new Thread(() -> {
                for (int i = 0; i < 10; i++) {
                    defaultFilterButtonsProvider.getButtons(mockUser, new ArrayList<>());
                }
            });
            Thread thread2 = new Thread(() -> {
                for (int i = 0; i < 10; i++) {
                    defaultFilterButtonsProvider.getDescribeApiName();
                }
            });

            thread1.start();
            thread2.start();

            thread1.join(1000);
            thread2.join(1000);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(defaultFilterButtonsProvider);
        assertNotNull(mockUser);
    }
}
