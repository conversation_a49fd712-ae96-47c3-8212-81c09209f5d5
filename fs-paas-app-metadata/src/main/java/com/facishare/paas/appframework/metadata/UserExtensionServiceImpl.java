package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.metadata.dto.userExtension.BatchRefreshHomePageLayoutBiI18nKey;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.rest.RestRequestFilter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class UserExtensionServiceImpl implements UserExtensionService {

    @Resource
    private UserExtensionProxy userExtensionProxy;

    @Override
    public Set<String> brushBiCardI18nKey(Integer routeTenantId, Set<String> enterpriseIdList) {
        if (enterpriseIdList == null || enterpriseIdList.isEmpty()) {
            log.warn("brushBiCardI18nKey warn! routeTenantId: {}, enterpriseIdList: {}", routeTenantId, enterpriseIdList);
            return new HashSet<>();
        }
        Map<String, String> headers = builderHeaders(routeTenantId);
        BatchRefreshHomePageLayoutBiI18nKey.Result result = new BatchRefreshHomePageLayoutBiI18nKey.Result();
        try {
            result = userExtensionProxy.batchRefreshHomePageLayoutBiI18nKey(headers, BatchRefreshHomePageLayoutBiI18nKey.Arg
                    .builder()
                    .routeTenantId(String.valueOf(routeTenantId))
                    .tenantIdList(Lists.newArrayList(enterpriseIdList))
                    .build());
        } catch (Exception e) {
            log.warn("batchRefreshHomePageLayoutBiI18nKey error!routeTenantId:{},enterpriseIdList:{}", routeTenantId,
                    enterpriseIdList, e);
            return enterpriseIdList;
        }
        return result.getFailTenantIdList();
    }

    private Map<String, String> builderHeaders(Integer routeTenantId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put(InnerHeaders.TENANT_ID, String.valueOf(routeTenantId));
        headers.put(InnerHeaders.USER_ID, String.valueOf(-10000));
        return headers;
    }
}
