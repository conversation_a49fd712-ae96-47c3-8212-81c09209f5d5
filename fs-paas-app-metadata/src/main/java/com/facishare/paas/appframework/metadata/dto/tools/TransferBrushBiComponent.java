package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

public interface TransferBrushBiComponent {
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Arg implements Serializable {
        List<String> enterpriseIds;
        int brushScope; // 0 刷指定企业 1刷系统库 2 代表全网刷所有企业
        String envTenantRouter;
        List<Integer> envList; //要刷的环境

    }
}
